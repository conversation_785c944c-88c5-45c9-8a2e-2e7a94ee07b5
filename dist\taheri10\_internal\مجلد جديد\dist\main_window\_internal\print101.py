import os
import sys
import sqlite3
import traceback
from datetime import datetime
import subprocess

def get_database_path():
    """
    الحصول على مسار قاعدة البيانات الصحيح
    يعمل في البيئة العادية وبعد التحزيم
    """
    if getattr(sys, 'frozen', False):
        # البرنامج محزم - البحث في مجلد البرنامج الرئيسي
        application_path = os.path.dirname(sys.executable)
    else:
        # البرنامج يعمل من المصدر
        application_path = os.path.dirname(os.path.abspath(__file__))

    db_path = os.path.join(application_path, 'data.db')

    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(db_path):
        # محاولة البحث في مجلد أعلى
        parent_path = os.path.dirname(application_path)
        alternative_db_path = os.path.join(parent_path, 'data.db')
        if os.path.exists(alternative_db_path):
            return alternative_db_path

    return db_path
import json

# ================= إعدادات خاصة بتقارير التلاميذ =================
# الجدول الأول: معلومات التلميذ والتمدرس
COL_WIDTHS_TABLE1 = [40, 40, 70, 40]  # 4 أعمدة
# الجدول الثاني: واجبات التسجيل
COL_WIDTHS_TABLE2 = [40, 30, 30, 25, 30, 35]  # 6 أعمدة
# الجدول الثالث: الواجبات الشهرية
COL_WIDTHS_TABLE3 = [25, 20, 30, 30, 30, 25, 30]  # 7 أعمدة
# الجدول الرابع: ملخص الحالة المالية
COL_WIDTHS_TABLE4 = [30, 65, 30, 65]  # 4 أعمدة

# عناوين الجداول
TABLE1_HEADERS = ['القيمة', 'البيان', 'القيمة', 'البيان']
TABLE2_HEADERS = ['تاريخ الإضافة', 'طريقة الدفع', 'رقم القسط', 'تاريخ الدفع', 'المبلغ المدفوع', 'نوع الدفعة']
TABLE3_HEADERS = ['ملاحظات', 'حالة الدفع', 'تاريخ الدفع', 'المبلغ المتبقي', 'المبلغ المدفوع', 'السنة', 'الشهر']
TABLE4_HEADERS = ['القيمة', 'البيان المالي', 'القيمة', 'البيان المالي']

# إضافة ارتفاعات الصفوف
ROW_HEIGHT_TABLE1 = 8   # ارتفاع صفوف الجدول الأول
ROW_HEIGHT_TABLE2 = 7   # ارتفاع صفوف الجدول الثاني
ROW_HEIGHT_TABLE3 = 6   # ارتفاع صفوف الجدول الثالث
ROW_HEIGHT_TABLE4 = 9   # ارتفاع صفوف الجدول الرابع
ROW_HEIGHT_HEADER = 10  # ارتفاع صفوف الرأس
ROW_HEIGHT_TABLE_HEADER = 12  # ارتفاع صف رأس الجدول

# إعدادات الهوامش
PAGE_MARGIN_TOP = 0.2
PAGE_MARGIN_BOTTOM = 0.2
PAGE_MARGIN_LEFT = 10
PAGE_MARGIN_RIGHT = 10

PT_TO_MM = 0.3528
LOGO_W_PT, LOGO_H_PT = 200, 80
BOX1_W_PT, BOX2_W_PT = 380, 170
TITLE_H_PT = 40
LOGO_W = LOGO_W_PT * PT_TO_MM
LOGO_H = LOGO_H_PT * PT_TO_MM
BOX1_W = BOX1_W_PT * PT_TO_MM
BOX2_W = BOX2_W_PT * PT_TO_MM
BOX_H = TITLE_H_PT * PT_TO_MM

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display

class ArabicPDF(FPDF):
    def __init__(self):
        super().__init__('P','mm','A4')
        self.set_margins(PAGE_MARGIN_LEFT, PAGE_MARGIN_TOP, PAGE_MARGIN_RIGHT)
        self.set_auto_page_break(auto=True, margin=PAGE_MARGIN_BOTTOM)
        fonts_dir = os.path.join(os.path.dirname(__file__), 'fonts')
        
        # إضافة خطوط Calibri إذا كانت متوفرة
        calibri_path = os.path.join(fonts_dir, 'calibri.ttf')
        calibri_bold_path = os.path.join(fonts_dir, 'calibrib.ttf')
        
        if os.path.exists(calibri_path):
            self.add_font('Calibri', '', calibri_path)
            self.calibri_available = True
        else:
            self.calibri_available = False
            
        if os.path.exists(calibri_bold_path):
            self.add_font('Calibri', 'B', calibri_bold_path)
            self.calibri_bold_available = True
        else:
            self.calibri_bold_available = False
        
        # إضافة خطوط Arial كبديل
        arial_path = os.path.join(fonts_dir, 'arial.ttf')
        arial_bold_path = os.path.join(fonts_dir, 'arialbd.ttf')
        
        if os.path.exists(arial_path):
            self.add_font('Arial', '', arial_path)
        if os.path.exists(arial_bold_path):
            self.add_font('Arial', 'B', arial_bold_path)
            
        # تعيين الخط الافتراضي
        if self.calibri_available:
            self.set_font('Calibri', '', 13)
        else:
            self.set_font('Arial', '', 13)
        self.set_line_width(0.4)

    def set_title_font(self, size=15):
        """تعيين خط العناوين - مكافئ QFont("Calibri", 15, QFont.Bold)"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_detail_font(self, size=13):
        """تعيين خط التفاصيل - مكافئ QFont("Calibri", 13, QFont.Bold)"""
        if self.calibri_bold_available:
            self.set_font('Calibri', 'B', size)
        else:
            self.set_font('Arial', 'B', size)

    def set_normal_font(self, size=12):
        """تعيين الخط العادي"""
        if self.calibri_available:
            self.set_font('Calibri', '', size)
        else:
            self.set_font('Arial', '', size)

    def ar_text(self, txt: str) -> str:
        """تحويل النص العربي ليتم عرضه بشكل صحيح"""
        reshaped = arabic_reshaper.reshape(str(txt))
        return get_display(reshaped)

def generate_student_detailed_report(logo_path, student_data, registration_fees, monthly_duties, output_path):
    """إنشاء تقرير مفصل عن التلميذ"""
    pdf = ArabicPDF()
    margin = 10
    usable_w = pdf.w - 2 * margin

    pdf.add_page()
    y = pdf.get_y()
    
    # إضافة الشعار
    if logo_path:
        x_logo = (pdf.w - LOGO_W) / 2
        pdf.image(logo_path, x=x_logo, y=y, w=LOGO_W, h=LOGO_H)
    y += LOGO_H + 2

    # عنوان التقرير - استخدام خط العناوين
    pdf.set_draw_color(0, 51, 102)  # لون أزرق داكن
    pdf.set_line_width(0.5)
    FIXED_BOX_HEIGHT = 12

    x = margin
    student_name = student_data.get('اسم_التلميذ', 'غير محدد')
    student_id = student_data.get('id', 'غير محدد')
    title_text = f"تقرير مفصل عن التلميذ: {student_name} - ID: {student_id}"
    
    pdf.set_text_color(0, 51, 102)  # لون أزرق داكن للنص

    # مربع العنوان يمتد على كامل العرض - استخدام خط العناوين
    pdf.set_xy(x, y)
    pdf.set_title_font(15)  # QFont("Calibri", 15, QFont.Bold)
    pdf.cell(usable_w, FIXED_BOX_HEIGHT, pdf.ar_text(title_text), border=1, align='C')

    pdf.set_text_color(0, 0, 0)  # إعادة اللون للأسود
    y += FIXED_BOX_HEIGHT + 0

    # الجدول الأول: معلومات التلميذ والتمدرس
    cols1 = COL_WIDTHS_TABLE1
    
    # دالة مساعدة لتحويل القيم إلى أرقام عشرية بأمان
    def safe_float(value, default=0):
        """تحويل القيمة إلى رقم عشري بأمان"""
        if value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            # إذا كانت القيمة تاريخ أو نص، إرجاع القيمة الافتراضية
            return default

    # إعداد البيانات للجدول الأول (صفوف متعددة)
    student_info_rows = [
        [student_data.get('رمز_التلميذ', 'غير محدد'), 'رمز التلميذ', str(student_data.get('id', 'غير محدد')), 'معرف التلميذ'],
        [student_data.get('النوع', 'غير محدد'), 'النوع', student_data.get('اسم_التلميذ', 'غير محدد'), 'اسم التلميذ'],
        [student_data.get('رقم_الهاتف_الثاني', 'غير محدد'), 'الهاتف الثاني', student_data.get('رقم_الهاتف_الأول', 'غير محدد'), 'الهاتف الأول'],
        [student_data.get('القسم', 'غير محدد'), 'القسم', student_data.get('اسم_المجموعة', 'غير محدد'), 'المجموعة'],
        [f"{safe_float(student_data.get('اجمالي_مبلغ_التسجيل', 0)):.2f} درهم", 'مبلغ التسجيل', student_data.get('المؤسسة_الاصلية', 'غير محدد'), 'المؤسسة الأصلية'],
        [f"{safe_float(student_data.get('المبلغ_النهائي_الشهري', 0)):.2f} درهم", 'المبلغ الشهري النهائي', f"{safe_float(student_data.get('الواجب_الشهري', 0)):.2f} درهم", 'الواجب الشهري']
    ]
    
    pdf.set_detail_font(13)  # QFont("Calibri", 13, QFont.Bold)
    pdf.set_fill_color(230, 240, 255)  # خلفية زرقاء فاتحة
    
    # رسم صفوف معلومات التلميذ
    for row in student_info_rows:
        x = margin
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = i % 2 == 1
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols1[i], ROW_HEIGHT_TABLE1, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols1[i]
        y += ROW_HEIGHT_TABLE1
        
    y += 2

    # الجدول الثاني: واجبات التسجيل
    pdf.set_title_font(14)  # خط العناوين للعنوان الفرعي
    pdf.set_text_color(0, 100, 0)  # لون أخضر
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('واجبات التسجيل والأقساط'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)  # إعادة اللون للأسود
    y += 10

    cols2 = COL_WIDTHS_TABLE2
    
    pdf.set_detail_font(13)  # خط التفاصيل لرأس الجدول
    pdf.set_fill_color(200, 255, 200)  # خلفية خضراء فاتحة للرأس

    # رسم رأس الجدول الثاني
    x = margin
    for i, header in enumerate(TABLE2_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols2[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols2[i]

    y += ROW_HEIGHT_TABLE_HEADER
    pdf.set_normal_font(12)  # خط عادي للمحتوى

    # محتوى جدول واجبات التسجيل
    if registration_fees:
        for i, fee in enumerate(registration_fees):
            x = margin
            data = [
                fee[6] or '',  # تاريخ الإضافة
                fee[4] or 'غير محدد',  # طريقة الدفع
                str(fee[3]) if fee[3] else '',  # رقم القسط
                fee[2] or 'غير محدد',  # تاريخ الدفع
                f'{safe_float(fee[1]):.2f} درهم' if fee[1] else '0.00 درهم',  # المبلغ المدفوع
                fee[0] or ''  # نوع الدفعة
            ]

            # تلوين متناوب
            if i % 2 == 0:
                pdf.set_fill_color(245, 255, 245)
            else:
                pdf.set_fill_color(235, 255, 235)

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols2[j], ROW_HEIGHT_TABLE2, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols2[j]

            y += ROW_HEIGHT_TABLE2

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 80:
                pdf.add_page()
                y = pdf.get_y()
    else:
        # إذا لم توجد واجبات تسجيل
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols2), ROW_HEIGHT_TABLE2, pdf.ar_text('لا توجد واجبات تسجيل مسجلة'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE2

    y += 2

    # الجدول الثالث: الواجبات الشهرية
    pdf.set_title_font(14)  # خط العناوين للعنوان الفرعي
    pdf.set_text_color(128, 0, 128)  # لون بنفسجي
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('الواجبات الشهرية'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)  # إعادة اللون للأسود
    y += 10

    cols3 = COL_WIDTHS_TABLE3
    
    pdf.set_detail_font(13)  # خط التفاصيل لرأس الجدول
    pdf.set_fill_color(255, 200, 255)  # خلفية بنفسجية فاتحة للرأس

    # رسم رأس الجدول الثالث
    x = margin
    for i, header in enumerate(TABLE3_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols3[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols3[i]

    y += ROW_HEIGHT_TABLE_HEADER
    pdf.set_normal_font(11)  # خط عادي أصغر للمحتوى

    # محتوى جدول الواجبات الشهرية
    if monthly_duties:
        for i, duty in enumerate(monthly_duties):
            x = margin
            data = [
                duty[7] or '',  # ملاحظات
                duty[6] or 'غير محدد',  # حالة الدفع
                duty[5] or 'غير محدد',  # تاريخ الدفع
                f'{safe_float(duty[4]):.2f} درهم' if duty[4] else '0.00 درهم',  # المبلغ المتبقي
                f'{safe_float(duty[3]):.2f} درهم' if duty[3] else '0.00 درهم',  # المبلغ المدفوع
                str(duty[1]) if duty[1] else '',  # السنة
                duty[0] or ''  # الشهر
            ]

            # تلوين حسب حالة الدفع
            if duty[6] == "مدفوع كاملاً":
                pdf.set_fill_color(220, 255, 220)  # أخضر فاتح
            elif duty[6] == "مدفوع جزئياً":
                pdf.set_fill_color(255, 255, 200)  # أصفر فاتح
            else:
                pdf.set_fill_color(255, 220, 220)  # أحمر فاتح

            for j, cell in enumerate(data):
                pdf.set_xy(x, y)
                pdf.cell(cols3[j], ROW_HEIGHT_TABLE3, pdf.ar_text(cell), border=1, align='C', fill=True)
                x += cols3[j]

            y += ROW_HEIGHT_TABLE3

            # الانتقال إلى صفحة جديدة عند الحاجة
            if y > pdf.h - 50:
                pdf.add_page()
                y = pdf.get_y()
    else:
        # إذا لم توجد واجبات شهرية
        pdf.set_xy(margin, y)
        pdf.set_fill_color(255, 245, 245)
        pdf.cell(sum(cols3), ROW_HEIGHT_TABLE3, pdf.ar_text('لا توجد واجبات شهرية مسجلة'), border=1, align='C', fill=True)
        y += ROW_HEIGHT_TABLE3

    # إضافة ملخص مالي في النهاية
    y += 2
    pdf.set_title_font(14)  # خط العناوين للملخص المالي
    pdf.set_text_color(0, 51, 102)
    pdf.set_xy(margin, y)
    pdf.cell(usable_w, 8, pdf.ar_text('الملخص المالي'), border=0, align='C')
    pdf.set_text_color(0, 0, 0)
    y += 10

    # حساب الإحصائيات
    total_registration_paid = sum([safe_float(fee[1]) for fee in registration_fees if fee[1]]) if registration_fees else 0
    total_monthly_paid = sum([safe_float(duty[3]) for duty in monthly_duties if duty[3]]) if monthly_duties else 0
    total_monthly_remaining = sum([safe_float(duty[4]) for duty in monthly_duties if duty[4]]) if monthly_duties else 0
    total_registration_required = safe_float(student_data.get("اجمالي_مبلغ_التسجيل", 0))
    registration_remaining = max(0, total_registration_required - total_registration_paid)

    # الجدول الرابع: ملخص الحالة المالية
    cols4 = COL_WIDTHS_TABLE4
    
    # رسم رأس الجدول الرابع
    pdf.set_detail_font(12)  # خط التفاصيل لرأس الجدول
    pdf.set_fill_color(255, 215, 0)  # خلفية ذهبية للرأس
    
    x = margin
    for i, header in enumerate(TABLE4_HEADERS):
        pdf.set_xy(x, y)
        pdf.cell(cols4[i], ROW_HEIGHT_TABLE_HEADER, pdf.ar_text(header), border=1, align='C', fill=True)
        x += cols4[i]

    y += ROW_HEIGHT_TABLE_HEADER

    # محتوى الجدول الرابع - ملخص الحالة المالية
    summary_data = [
        [f'{total_registration_paid:.2f} درهم', 'إجمالي المدفوع للتسجيل', f'{total_registration_required:.2f} درهم', 'إجمالي واجبات التسجيل'],
        [f'{registration_remaining:.2f} درهم', 'المتبقي من واجبات التسجيل', f'{total_monthly_paid:.2f} درهم', 'إجمالي المدفوع للواجبات الشهرية'],
        [f'{total_monthly_remaining:.2f} درهم', 'المتبقي من الواجبات الشهرية', f'{(total_registration_paid + total_monthly_paid):.2f} درهم', 'إجمالي المبالغ المدفوعة'],
        [f'{(registration_remaining + total_monthly_remaining):.2f} درهم', 'إجمالي المبالغ المتبقية', f'{(total_registration_required + total_monthly_paid + total_monthly_remaining):.2f} درهم', 'إجمالي الالتزامات المالية']
    ]

    pdf.set_detail_font(11)  # خط التفاصيل للملخص
    
    for row_idx, row in enumerate(summary_data):
        x = margin
        # تلوين متناوب للصفوف
        if row_idx % 2 == 0:
            pdf.set_fill_color(255, 248, 220)  # لون كريمي فاتح
        else:
            pdf.set_fill_color(255, 239, 213)  # لون كريمي أغمق قليلاً
            
        for i, cell in enumerate(row):
            pdf.set_xy(x, y)
            fill = True
            align = 'C' if i % 2 == 1 else 'R'
            pdf.cell(cols4[i], ROW_HEIGHT_TABLE4, pdf.ar_text(cell), border=1, align=align, fill=fill)
            x += cols4[i]
        y += ROW_HEIGHT_TABLE4

    # إضافة تاريخ الطباعة والتوقيع
    y += 2
    current_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pdf.set_normal_font(10)  # خط عادي للتاريخ
    pdf.set_xy(margin, y)
    pdf.cell(usable_w/2, 8, pdf.ar_text(f'تاريخ الطباعة: {current_date}'), border=0, align='R')
    
    # مساحة للتوقيع
    signature_width = usable_w / 3
    pdf.set_xy(margin + usable_w - signature_width, y + 10)
    pdf.cell(signature_width, 15, '', border=1, align='C')
    
    pdf.set_xy(margin + usable_w - signature_width, y + 27)
    pdf.cell(signature_width, 8, pdf.ar_text('توقيع المسؤول'), border=0, align='C')

    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    pdf.output(output_path)
    print(f"تم إنشاء التقرير المفصل: {output_path}")

def print_student_detailed_report(parent=None, student_id=None):
    """
    دالة لإنشاء تقرير مفصل عن التلميذ
    
    المعاملات:
        parent: كائن النافذة الأم (لعرض رسائل)
        student_id: معرف التلميذ

    العوائد:
        (success, output_path, message): ثلاثية تحدد نجاح العملية ومسار الملف ورسالة النتيجة
    """
    try:
        if not student_id:
            return False, None, "معرف التلميذ غير محدد."

        # تحديد مسار قاعدة البيانات
        db_path = get_database_path()
        
        # جلب بيانات التلميذ الكاملة
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # جلب شعار المؤسسة
            cursor.execute("SELECT ImagePath1 FROM بيانات_المؤسسة LIMIT 1")
            logo_row = cursor.fetchone()
            logo_path = logo_row[0] if logo_row and os.path.exists(logo_row[0]) else None
            
            # جلب بيانات التلميذ الأساسية
            cursor.execute("SELECT * FROM جدول_البيانات WHERE id = ?", (student_id,))
            student_row = cursor.fetchone()
            
            if not student_row:
                conn.close()
                return False, None, f"لا يمكن العثور على التلميذ بالمعرف: {student_id}"
            
            # تحويل البيانات إلى قاموس
            student_data = {
                'id': student_row[0],
                'اسم_التلميذ': student_row[1],
                'رمز_التلميذ': student_row[2],
                'النوع': student_row[3],
                'رقم_الهاتف_الأول': student_row[4],
                'رقم_الهاتف_الثاني': student_row[5],
                'ملاحظات': student_row[6],
                'اسم_المجموعة': student_row[7],
                'القسم': student_row[8],
                'المؤسسة_الاصلية': student_row[9],
                'اجمالي_مبلغ_التسجيل': student_row[10] or 0,
                'عدد_الاقساط': student_row[11],
                'مبلغ_القسط': student_row[12],
                'الواجب_الشهري': student_row[13] or 0,
                'الاشهر_المحددة': student_row[14],
                'المبلغ_النهائي_الشهري': student_row[15] or 0
            }
            
            # جلب واجبات التسجيل
            registration_fees = []
            cursor.execute("""
                SELECT payment_type, amount_paid, payment_date, 
                       installment_number, payment_method, notes, created_date
                FROM registration_fees 
                WHERE student_id = ? 
                ORDER BY created_date DESC, installment_number ASC
            """, (student_id,))
            registration_fees = cursor.fetchall()
            
            # جلب الواجبات الشهرية
            monthly_duties = []
            cursor.execute("""
                SELECT month, year, amount_required, amount_paid, amount_remaining,
                       payment_date, payment_status, notes, created_date
                FROM monthly_duties 
                WHERE student_id = ? 
                ORDER BY year DESC, 
                CASE month 
                    WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
                    WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
                    WHEN 'يوليو' THEN 7 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
                    WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
                END DESC
            """, (student_id,))
            monthly_duties = cursor.fetchall()
            
            conn.close()
            
        except Exception as db_error:
            print(f"خطأ في الوصول لقاعدة البيانات: {db_error}")
            return False, None, f"خطأ في الوصول لقاعدة البيانات: {str(db_error)}"

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        reports_dir = os.path.join(os.path.expanduser('~'), 'Desktop', 'تقارير التلاميذ المفصلة')
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # تحديد اسم الملف
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        student_name = student_data.get('اسم_التلميذ', 'تلميذ').replace(' ', '_')
        output_path = os.path.join(reports_dir, f"تقرير_مفصل_{student_name}_ID_{student_id}_{timestamp}.pdf")

        # إنشاء التقرير
        generate_student_detailed_report(logo_path, student_data, registration_fees, monthly_duties, output_path)

        # فتح الملف بعد إنشائه
        try:
            if sys.platform == 'win32':
                os.startfile(output_path)
            elif sys.platform == 'darwin':  # macOS
                subprocess.call(['open', output_path])
            else:  # Linux
                subprocess.call(['xdg-open', output_path])
        except Exception as e:
            return True, output_path, f"تم إنشاء التقرير ولكن تعذر فتح الملف: {str(e)}"

        return True, output_path, "تم إنشاء التقرير المفصل بنجاح."
        
    except Exception as e:
        traceback.print_exc()
        return False, None, f"حدث خطأ في إنشاء التقرير المفصل: {str(e)}"

if __name__=='__main__':
    # مثال على الاستخدام
    try:
        success, output_path, message = print_student_detailed_report(student_id=1)
        
        print(f"النتيجة: {success}")
        print(f"المسار: {output_path}")
        print(f"الرسالة: {message}")
        
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        traceback.print_exc()
