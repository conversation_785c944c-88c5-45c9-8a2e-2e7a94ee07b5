2025-06-18 17:51:46 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 17:51:46 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 17:51:47 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 17:52:40 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 17:52:49 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 17:52:49 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 17:52:49 - TeachersReg<PERSON>ryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 17:52:58 - <PERSON><PERSON><PERSON><PERSON>ryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:01:50 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:01:50 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:01:51 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:14:02 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:14:02 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:14:03 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:14:08 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:19:33 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:19:34 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:19:34 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:19:53 - TeachersRegistryWindow - ERROR - خطأ في تحديث نسبة الأستاذ: no such column: الشهر
2025-06-18 18:19:53 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 824, in update_monthly_duties_percentage
    cursor.execute("""
    ~~~~~~~~~~~~~~^^^^
        UPDATE monthly_duties
        ^^^^^^^^^^^^^^^^^^^^^
        SET نسبة_الاستاذ = ?
        ^^^^^^^^^^^^^^^^^^^^
        WHERE الشهر = ? AND القسم = ? AND السنة_الدراسية = ?
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    """, (duties_percent, month_number, section_name, current_year))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: الشهر

2025-06-18 18:19:56 - TeachersRegistryWindow - ERROR - خطأ في طباعة تقرير القسم الشهري: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'
2025-06-18 18:19:56 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 860, in print_section_monthly_report
    printer = print1_section_monthly.SectionMonthlyPrinter(self.db_path)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'

2025-06-18 18:20:00 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:21:05 - TeachersRegistryWindow - ERROR - خطأ في تحديث نسبة الأستاذ: no such column: الشهر
2025-06-18 18:21:05 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 824, in update_monthly_duties_percentage
    cursor.execute("""
    ~~~~~~~~~~~~~~^^^^
        UPDATE monthly_duties
        ^^^^^^^^^^^^^^^^^^^^^
        SET نسبة_الاستاذ = ?
        ^^^^^^^^^^^^^^^^^^^^
        WHERE الشهر = ? AND القسم = ? AND السنة_الدراسية = ?
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    """, (duties_percent, month_number, section_name, current_year))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: الشهر

2025-06-18 18:21:09 - TeachersRegistryWindow - ERROR - خطأ في طباعة تقرير القسم الشهري: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'
2025-06-18 18:21:09 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 860, in print_section_monthly_report
    printer = print1_section_monthly.SectionMonthlyPrinter(self.db_path)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'

2025-06-18 18:21:11 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
