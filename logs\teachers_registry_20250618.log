2025-06-18 17:51:46 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 17:51:46 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 17:51:47 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 17:52:40 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 17:52:49 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 17:52:49 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 17:52:49 - TeachersReg<PERSON>ryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 17:52:58 - <PERSON><PERSON><PERSON><PERSON>ryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:01:50 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:01:50 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:01:51 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:14:02 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:14:02 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:14:03 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:14:08 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:19:33 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:19:34 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:19:34 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:19:53 - TeachersRegistryWindow - ERROR - خطأ في تحديث نسبة الأستاذ: no such column: الشهر
2025-06-18 18:19:53 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 824, in update_monthly_duties_percentage
    cursor.execute("""
    ~~~~~~~~~~~~~~^^^^
        UPDATE monthly_duties
        ^^^^^^^^^^^^^^^^^^^^^
        SET نسبة_الاستاذ = ?
        ^^^^^^^^^^^^^^^^^^^^
        WHERE الشهر = ? AND القسم = ? AND السنة_الدراسية = ?
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    """, (duties_percent, month_number, section_name, current_year))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: الشهر

2025-06-18 18:19:56 - TeachersRegistryWindow - ERROR - خطأ في طباعة تقرير القسم الشهري: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'
2025-06-18 18:19:56 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 860, in print_section_monthly_report
    printer = print1_section_monthly.SectionMonthlyPrinter(self.db_path)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'

2025-06-18 18:20:00 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:20:23 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:21:05 - TeachersRegistryWindow - ERROR - خطأ في تحديث نسبة الأستاذ: no such column: الشهر
2025-06-18 18:21:05 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 824, in update_monthly_duties_percentage
    cursor.execute("""
    ~~~~~~~~~~~~~~^^^^
        UPDATE monthly_duties
        ^^^^^^^^^^^^^^^^^^^^^
        SET نسبة_الاستاذ = ?
        ^^^^^^^^^^^^^^^^^^^^
        WHERE الشهر = ? AND القسم = ? AND السنة_الدراسية = ?
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    """, (duties_percent, month_number, section_name, current_year))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: الشهر

2025-06-18 18:21:09 - TeachersRegistryWindow - ERROR - خطأ في طباعة تقرير القسم الشهري: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'
2025-06-18 18:21:09 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 860, in print_section_monthly_report
    printer = print1_section_monthly.SectionMonthlyPrinter(self.db_path)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'

2025-06-18 18:21:11 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:22:15 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:22:16 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:22:16 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:22:34 - TeachersRegistryWindow - ERROR - خطأ في تحديث نسبة الأستاذ: no such column: الشهر
2025-06-18 18:22:34 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 823, in update_monthly_duties_percentage
    month_names_en = {
    ...<3 lines>...
    
sqlite3.OperationalError: no such column: الشهر

2025-06-18 18:22:36 - TeachersRegistryWindow - ERROR - خطأ في طباعة تقرير القسم الشهري: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'
2025-06-18 18:22:36 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 859, in print_section_monthly_report
AttributeError: module 'print1_section_monthly' has no attribute 'SectionMonthlyPrinter'

2025-06-18 18:22:41 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:24:05 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:24:06 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:24:06 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:24:29 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:30:28 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:30:28 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:30:28 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:30:38 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:30:56 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:30:57 - TeachersRegistryWindow - CRITICAL - خطأ في تهيئة النافذة: name 'layout' is not defined
2025-06-18 18:30:57 - TeachersRegistryWindow - DEBUG - تفاصيل الخطأ الكاملة:
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 38, in __init__
    self.setupUI()
    ~~~~~~~~~~~~^^
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 146, in setupUI
    self.setup_teachers_registry_tab(main_layout)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\taheri10\sub26662_window.py", line 384, in setup_teachers_registry_tab
    layout.addWidget(registry_frame)
    ^^^^^^
NameError: name 'layout' is not defined. Did you mean: 'self.layout'?

2025-06-18 18:31:05 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:31:10 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:31:11 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:31:11 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:31:29 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:31:44 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:31:45 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:31:45 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:31:58 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:32:33 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:32:34 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:32:34 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:33:02 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:33:54 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:33:55 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:33:55 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:34:17 - TeachersRegistryWindow - INFO - العملية: طباعة تقرير القسم الشهري - التفاصيل: القسم: قسم / 02, الشهر: January
2025-06-18 18:34:52 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:34:53 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:34:53 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:35:30 - TeachersRegistryWindow - INFO - العملية: طباعة تقرير القسم الشهري - التفاصيل: القسم: قسم / 02, الشهر: January
2025-06-18 18:35:31 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:37:15 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:37:16 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:37:16 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:37:46 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
2025-06-18 18:38:20 - TeachersRegistryWindow - INFO - بدء تشغيل نافذة سجلات الأساتذة
2025-06-18 18:38:21 - TeachersRegistryWindow - INFO - العملية: تحميل سجلات الأساتذة
2025-06-18 18:38:21 - TeachersRegistryWindow - INFO - تم تحميل النافذة بنجاح
2025-06-18 18:38:30 - TeachersRegistryWindow - INFO - العملية: تحديث نسبة الأستاذ - التفاصيل: القسم: قسم / 02, الشهر: يناير, النسبة: 50%
2025-06-18 18:38:54 - TeachersRegistryWindow - INFO - العملية: طباعة تقرير القسم الشهري - التفاصيل: القسم: قسم / 02, الشهر: January
2025-06-18 18:38:55 - TeachersRegistryWindow - INFO - العملية: إغلاق النافذة
