#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import logging
import traceback
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem, 
    QFrame, QMessageBox, QComboBox, QFormLayout, QGroupBox, 
    QHeaderView, QInputDialog, QAbstractItemView, QSplitter,
    QTextEdit, QTabWidget, QSizePolicy, QDialog, QDialogButtonBox
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette
from PyQt5.QtCore import Qt, QSize

class TeachersRegistryWindow(QMainWindow):
    """نافذة سجلات الأساتذة المخصصة"""
    
    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        # تحديد مسار قاعدة البيانات
        if db_path:
            self.db_path = db_path
        elif parent and hasattr(parent, 'db_path'):
            self.db_path = parent.db_path
        else:
            # استخدام data.db في مجلد البرنامج
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(script_dir, "data.db")
        self.setup_logging()
        self.logger.info("بدء تشغيل نافذة سجلات الأساتذة")
        
        try:
            self.setupUI()
            self.setup_database()
            self.load_teachers_registry()
            self.center_on_screen()
            self.logger.info("تم تحميل النافذة بنجاح")
        except Exception as e:
            self.handle_critical_error("خطأ في تهيئة النافذة", e)
    
    def setup_logging(self):
        """إعداد نظام التسجيل لاستكشاف الأخطاء"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            log_dir = os.path.join(os.path.dirname(__file__), "logs")
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # إعداد ملف السجل
            log_file = os.path.join(log_dir, f"teachers_registry_{datetime.now().strftime('%Y%m%d')}.log")
            
            # إعداد المسجل
            self.logger = logging.getLogger("TeachersRegistryWindow")
            self.logger.setLevel(logging.DEBUG)
            
            # تنسيق الرسائل
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # معالج الملف
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            
            # معالج وحدة التحكم
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            
            # إضافة المعالجات
            if not self.logger.handlers:
                self.logger.addHandler(file_handler)
                self.logger.addHandler(console_handler)
                
        except Exception as e:
            print(f"فشل في إعداد نظام التسجيل: {str(e)}")
            # إنشاء مسجل بسيط كبديل
            self.logger = logging.getLogger("TeachersRegistryWindow")
            self.logger.setLevel(logging.INFO)
    
    def handle_critical_error(self, message, exception):
        """معالجة الأخطاء الحرجة"""
        error_details = f"{message}\n\nتفاصيل الخطأ:\n{str(exception)}\n\nالتتبع:\n{traceback.format_exc()}"
        self.logger.critical(f"{message}: {str(exception)}")
        self.logger.debug(f"تفاصيل الخطأ الكاملة:\n{traceback.format_exc()}")
        
        QMessageBox.critical(
            self, 
            "خطأ حرج", 
            f"{message}\n\n{str(exception)}\n\nيرجى التحقق من ملف السجل للحصول على مزيد من التفاصيل."
        )
    
    def handle_error(self, message, exception, show_user=True):
        """معالجة الأخطاء العادية"""
        error_msg = f"{message}: {str(exception)}"
        self.logger.error(error_msg)
        self.logger.debug(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        
        if show_user:
            QMessageBox.warning(self, "خطأ", f"{message}\n\n{str(exception)}")
    
    def log_operation(self, operation, details=""):
        """تسجيل العمليات"""
        log_msg = f"العملية: {operation}"
        if details:
            log_msg += f" - التفاصيل: {details}"
        self.logger.info(log_msg)

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📋 سجلات الأساتذة")
        self.setFixedSize(1350, 650)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # العنوان الرئيسي
        title_label = QLabel("📋 سجلات الأساتذة")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMaximumHeight(70)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1e88e5,
                    stop: 0.5 #1976d2,
                    stop: 1 #1e88e5
                );
                color: white;
                padding: 15px;
                border-radius: 12px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إعداد تبويب سجلات الأساتذة
        self.setup_teachers_registry_tab(main_layout)
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود الجداول المطلوبة
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('الاساتذة', 'المواد_الدراسية', 'الاقسام', 'المجموعات')
            """)
            
            existing_tables = [row[0] for row in cursor.fetchall()]
            required_tables = ['الاساتذة', 'المواد_الدراسية', 'الاقسام', 'المجموعات']
            
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                self.logger.warning(f"الجداول المفقودة: {missing_tables}")
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"بعض الجداول المطلوبة غير موجودة في قاعدة البيانات:\n{', '.join(missing_tables)}\n\nقد لا تعمل بعض الوظائف بشكل صحيح."
                )
            
            conn.close()
            
        except Exception as e:
            self.handle_error("خطأ في إعداد قاعدة البيانات", e)

    def setup_teachers_registry_tab(self, main_layout):
        """إعداد تبويب سجلات الأساتذة"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # قسم البحث والتصفية
        search_frame = QGroupBox("🔍 البحث والتصفية")
        search_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        search_frame.setStyleSheet(self.get_groupbox_style())
        search_frame.setMaximumHeight(120)

        search_layout = QHBoxLayout(search_frame)
        search_layout.setSpacing(15)

        # حقل البحث
        search_label = QLabel("البحث:")
        search_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_search_input = QLineEdit()
        self.registry_search_input.setPlaceholderText("ابحث بالاسم أو المادة أو القسم...")
        self.registry_search_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_search_input.setMinimumHeight(35)
        self.registry_search_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
            }
            QLineEdit:focus {
                border: 2px solid #7B1FA2;
                background-color: #f3e5f5;
            }
        """)
        self.registry_search_input.textChanged.connect(self.filter_teachers_registry)

        # تصفية حسب المادة
        filter_subject_label = QLabel("تصفية حسب المادة:")
        filter_subject_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_subject_filter = QComboBox()
        self.registry_subject_filter.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_subject_filter.setMinimumHeight(35)
        self.registry_subject_filter.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
            }
            QComboBox:focus {
                border: 2px solid #7B1FA2;
            }
        """)
        self.registry_subject_filter.currentTextChanged.connect(self.filter_teachers_registry)

        # تصفية حسب المجموعة
        filter_group_label = QLabel("تصفية حسب المجموعة:")
        filter_group_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_group_filter = QComboBox()
        self.registry_group_filter.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_group_filter.setMinimumHeight(35)
        self.registry_group_filter.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
            }
            QComboBox:focus {
                border: 2px solid #7B1FA2;
            }
        """)
        self.registry_group_filter.currentTextChanged.connect(self.filter_teachers_registry)

        # زر إعادة تعيين الفلاتر
        reset_filters_btn = self.create_styled_button("🔄 إعادة تعيين", "#FF5722")
        reset_filters_btn.clicked.connect(self.reset_registry_filters)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.registry_search_input, 2)
        search_layout.addWidget(filter_subject_label)
        search_layout.addWidget(self.registry_subject_filter, 1)
        search_layout.addWidget(filter_group_label)
        search_layout.addWidget(self.registry_group_filter, 1)
        search_layout.addWidget(reset_filters_btn)

        layout.addWidget(search_frame)

        # جدول سجلات الأساتذة
        registry_frame = QGroupBox("📊 سجلات الأساتذة")
        registry_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        registry_frame.setStyleSheet(self.get_groupbox_style())

        registry_layout = QVBoxLayout(registry_frame)

        # إنشاء جدول سجلات الأساتذة
        self.teachers_registry_table = QTableWidget()
        self.teachers_registry_table.setFont(QFont("Calibri", 12))
        self.teachers_registry_table.setAlternatingRowColors(True)
        self.teachers_registry_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.teachers_registry_table.horizontalHeader().setStretchLastSection(True)
        self.teachers_registry_table.verticalHeader().setVisible(False)
        self.teachers_registry_table.setStyleSheet(self.get_table_style())

        registry_layout.addWidget(self.teachers_registry_table)

        # إحصائيات سجلات الأساتذة
        self.registry_stats_label = QLabel()
        self.registry_stats_label.setFont(QFont("Calibri", 12, QFont.Bold))
        self.registry_stats_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #4caf50;
                border-radius: 8px;
                padding: 10px;
                color: #2e7d32;
            }
        """)
        registry_layout.addWidget(self.registry_stats_label)

        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        refresh_btn = self.create_styled_button("🔄 تحديث", "#4CAF50")
        refresh_btn.clicked.connect(self.refresh_teachers_registry)

        edit_btn = self.create_styled_button("✏️ تعديل سجل الأستاذ", "#2196F3")
        edit_btn.clicked.connect(self.edit_teacher_registry)

        delete_btn = self.create_styled_button("🗑️ حذف سجلات الأساتذة", "#f44336")
        delete_btn.clicked.connect(self.delete_teachers_registry)

        # زر طباعة سجلات الأساتذة
        print_btn = self.create_styled_button("🖨️ طباعة سجلات الأساتذة", "#795548")
        print_btn.clicked.connect(self.print_teachers_registry)

        # زر تصدير سجلات الأساتذة
        export_btn = self.create_styled_button("📤 تصدير السجلات", "#607D8B")
        export_btn.clicked.connect(self.export_teachers_registry)

        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_btn)
        buttons_layout.addStretch()

        registry_layout.addLayout(buttons_layout)

        layout.addWidget(registry_frame)

        # إضافة التخطيط إلى التخطيط الرئيسي
        main_layout.addLayout(layout)

    def get_groupbox_style(self):
        """إرجاع نمط GroupBox"""
        return """
            QGroupBox {
                font-family: 'Calibri';
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #2980b9;
                background-color: #f8f9fa;
            }
        """

    def get_table_style(self):
        """إرجاع نمط الجدول"""
        return """
            QTableWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                gridline-color: #ecf0f1;
                font-family: 'Calibri';
                font-size: 12px;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #ebf3fd;
            }
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                color: white;
                padding: 10px;
                border: none;
                font-family: 'Calibri';
                font-size: 13px;
                font-weight: bold;
            }
        """

    def create_styled_button(self, text, color):
        """إنشاء زر منسق"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 12, QFont.Bold))
        button.setMinimumHeight(40)
        button.setMinimumWidth(120)
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {self.darken_color(color)}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-family: 'Calibri';
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {self.lighten_color(color)},
                    stop: 1 {color}
                );
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {self.darken_color(color)};
                transform: translateY(1px);
            }}
        """)
        return button

    def darken_color(self, color):
        """تغميق اللون"""
        color_map = {
            "#4CAF50": "#45a049",
            "#2196F3": "#1976d2",
            "#f44336": "#d32f2f",
            "#FF9800": "#f57c00",
            "#9C27B0": "#7b1fa2",
            "#795548": "#5d4037",
            "#607D8B": "#455a64",
            "#FF5722": "#e64a19"
        }
        return color_map.get(color, color)

    def lighten_color(self, color):
        """تفتيح اللون"""
        color_map = {
            "#4CAF50": "#66bb6a",
            "#2196F3": "#42a5f5",
            "#f44336": "#ef5350",
            "#FF9800": "#ffa726",
            "#9C27B0": "#ab47bc",
            "#795548": "#8d6e63",
            "#607D8B": "#78909c",
            "#FF5722": "#ff7043"
        }
        return color_map.get(color, color)

    def load_teachers_registry(self):
        """تحميل سجلات الأساتذة"""
        try:
            self.log_operation("تحميل سجلات الأساتذة")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # الحصول على البيانات من جدول الاساتذة فقط
            cursor.execute('''
                SELECT
                    a.id,
                    a.اسم_الاستاذ,
                    m.اسم_المادة,
                    q.اسم_القسم,
                    g.اسم_المجموعة,
                    a.نسبة_الواجبات,
                    a.تاريخ_الاضافة
                FROM الاساتذة a
                LEFT JOIN المواد_الدراسية m ON a.مادة_id = m.id
                LEFT JOIN الاقسام q ON a.قسم_id = q.id
                LEFT JOIN المجموعات g ON a.مجموعة_id = g.id
                ORDER BY a.تاريخ_الاضافة DESC
            ''')

            teachers_data = cursor.fetchall()

            # تحديث الجدول
            self.teachers_registry_table.setColumnCount(8)
            self.teachers_registry_table.setHorizontalHeaderLabels([
                'رقم التسلسل', 'اسم الأستاذ', 'المادة', 'القسم', 'المجموعة', 'نسبة الواجبات', 'تاريخ التسجيل', 'الحالة'
            ])

            self.teachers_registry_table.setRowCount(len(teachers_data))

            for row, teacher in enumerate(teachers_data):
                teacher_id, name, subject, section, group, duties_percent, date_added = teacher

                # رقم التسلسل
                self.teachers_registry_table.setItem(row, 0, QTableWidgetItem(str(teacher_id)))

                # اسم الأستاذ
                self.teachers_registry_table.setItem(row, 1, QTableWidgetItem(name or "غير محدد"))

                # المادة
                self.teachers_registry_table.setItem(row, 2, QTableWidgetItem(subject or "غير محدد"))

                # القسم
                self.teachers_registry_table.setItem(row, 3, QTableWidgetItem(section or "غير محدد"))

                # المجموعة
                group_item = QTableWidgetItem(group or "غير محدد")
                group_item.setBackground(QColor("#f3e5f5"))  # لون خفيف بنفسجي للمجموعة
                self.teachers_registry_table.setItem(row, 4, group_item)

                # نسبة الواجبات
                duties_item = QTableWidgetItem(f"{duties_percent or 100}%")
                duties_item.setBackground(QColor("#e8f5e8"))  # لون خفيف أخضر للنسبة
                self.teachers_registry_table.setItem(row, 5, duties_item)

                # تاريخ التسجيل
                date_item = QTableWidgetItem(date_added or "غير محدد")
                date_item.setBackground(QColor("#fff3e0"))  # لون خفيف برتقالي للتاريخ
                self.teachers_registry_table.setItem(row, 6, date_item)

                # الحالة
                status_item = QTableWidgetItem("نشط")
                status_item.setBackground(QColor("#e8f5e8"))  # لون أخضر للحالة النشطة
                self.teachers_registry_table.setItem(row, 7, status_item)

            # تعيين عرض الأعمدة
            header = self.teachers_registry_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم التسلسل
            header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الأستاذ
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المادة
            header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # القسم
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # المجموعة
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # نسبة الواجبات
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التسجيل
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة

            # تحديث فلاتر البحث
            self.update_subject_filter()
            self.update_group_filter()

            # تحديث الإحصائيات
            self.update_registry_statistics()

            conn.close()

        except Exception as e:
            self.handle_error("خطأ في تحميل سجلات الأساتذة", e)

    def update_subject_filter(self):
        """تحديث قائمة تصفية المواد"""
        try:
            current_text = self.registry_subject_filter.currentText()
            self.registry_subject_filter.clear()
            self.registry_subject_filter.addItem("جميع المواد")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT اسم_المادة FROM المواد_الدراسية ORDER BY اسم_المادة")
            subjects = cursor.fetchall()

            for subject in subjects:
                self.registry_subject_filter.addItem(subject[0])

            # استعادة التحديد السابق
            index = self.registry_subject_filter.findText(current_text)
            if index >= 0:
                self.registry_subject_filter.setCurrentIndex(index)

            conn.close()
        except Exception as e:
            self.handle_error("خطأ في تحديث فلتر المواد", e)

    def update_group_filter(self):
        """تحديث قائمة تصفية المجموعات"""
        try:
            current_text = self.registry_group_filter.currentText()
            self.registry_group_filter.clear()
            self.registry_group_filter.addItem("جميع المجموعات")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT اسم_المجموعة FROM المجموعات ORDER BY اسم_المجموعة")
            groups = cursor.fetchall()

            for group in groups:
                self.registry_group_filter.addItem(group[0])

            # استعادة التحديد السابق
            index = self.registry_group_filter.findText(current_text)
            if index >= 0:
                self.registry_group_filter.setCurrentIndex(index)

            conn.close()

        except Exception as e:
            self.handle_error("خطأ في تحديث فلتر المجموعات", e)

    def update_registry_statistics(self):
        """تحديث إحصائيات سجلات الأساتذة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إجمالي عدد الأساتذة
            cursor.execute("SELECT COUNT(*) FROM الاساتذة")
            total_teachers = cursor.fetchone()[0]

            # عدد المواد المختلفة
            cursor.execute("""
                SELECT COUNT(DISTINCT مادة_id) FROM الاساتذة
                WHERE مادة_id IS NOT NULL
            """)
            total_subjects = cursor.fetchone()[0]

            # عدد الأقسام المختلفة
            cursor.execute("""
                SELECT COUNT(DISTINCT قسم_id) FROM الاساتذة
                WHERE قسم_id IS NOT NULL
            """)
            total_sections = cursor.fetchone()[0]

            # عدد المجموعات المختلفة
            cursor.execute("""
                SELECT COUNT(DISTINCT مجموعة_id) FROM الاساتذة
                WHERE مجموعة_id IS NOT NULL
            """)
            total_groups = cursor.fetchone()[0]

            # تحديث نص الإحصائيات
            stats_text = (
                f"📊 إحصائيات سجلات الأساتذة: "
                f"إجمالي الأساتذة: {total_teachers} | "
                f"المواد: {total_subjects} | "
                f"الأقسام: {total_sections} | "
                f"المجموعات: {total_groups}"
            )

            self.registry_stats_label.setText(stats_text)

            conn.close()

        except Exception as e:
            self.handle_error("خطأ في تحديث الإحصائيات", e)

    def filter_teachers_registry(self):
        """تصفية سجلات الأساتذة"""
        try:
            search_text = self.registry_search_input.text().lower()
            subject_filter = self.registry_subject_filter.currentText()
            group_filter = self.registry_group_filter.currentText()

            for row in range(self.teachers_registry_table.rowCount()):
                show_row = True

                # تطبيق فلتر البحث النصي
                if search_text:
                    row_text = ""
                    for col in range(self.teachers_registry_table.columnCount()):
                        item = self.teachers_registry_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    if search_text not in row_text:
                        show_row = False

                # تطبيق فلتر المادة
                if subject_filter and subject_filter != "جميع المواد":
                    subject_item = self.teachers_registry_table.item(row, 2)
                    if not subject_item or subject_item.text() != subject_filter:
                        show_row = False

                # تطبيق فلتر المجموعة
                if group_filter and group_filter != "جميع المجموعات":
                    group_item = self.teachers_registry_table.item(row, 4)
                    if not group_item or group_item.text() != group_filter:
                        show_row = False

                self.teachers_registry_table.setRowHidden(row, not show_row)

        except Exception as e:
            self.handle_error("خطأ في تصفية السجلات", e)

    def reset_registry_filters(self):
        """إعادة تعيين فلاتر البحث"""
        self.registry_search_input.clear()
        self.registry_subject_filter.setCurrentIndex(0)
        self.registry_group_filter.setCurrentIndex(0)

        # إظهار جميع الصفوف
        for row in range(self.teachers_registry_table.rowCount()):
            self.teachers_registry_table.setRowHidden(row, False)

    def refresh_teachers_registry(self):
        """تحديث سجلات الأساتذة"""
        self.load_teachers_registry()
        QMessageBox.information(self, "تم التحديث", "تم تحديث سجلات الأساتذة بنجاح.")

    def edit_teacher_registry(self):
        """تعديل سجل أستاذ محدد"""
        try:
            # التحقق من تحديد صف
            current_row = self.teachers_registry_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل أستاذ للتعديل.")
                return

            # استخراج بيانات السجل المحدد
            teacher_id = int(self.teachers_registry_table.item(current_row, 0).text())
            current_name = self.teachers_registry_table.item(current_row, 1).text()
            current_duties = self.teachers_registry_table.item(current_row, 5).text().replace('%', '')

            # إنشاء نافذة تعديل
            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل سجل الأستاذ")
            dialog.setFixedSize(400, 200)
            dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QFormLayout(dialog)

            # حقل اسم الأستاذ
            name_input = QLineEdit(current_name)
            name_input.setFont(QFont("Calibri", 12))
            name_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #2196F3;
                    border-radius: 5px;
                }
            """)

            # حقل النسبة
            duties_input = QLineEdit(current_duties)
            duties_input.setFont(QFont("Calibri", 12))
            duties_input.setInputMask("999")  # أرقام فقط
            duties_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #2196F3;
                    border-radius: 5px;
                }
            """)

            layout.addRow("اسم الأستاذ:", name_input)
            layout.addRow("نسبة الواجبات (%):", duties_input)

            # أزرار الحوار
            buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addRow(buttons)

            # تنفيذ الحوار
            if dialog.exec_() == QDialog.Accepted:
                new_name = name_input.text().strip()
                new_duties = duties_input.text().strip()

                if not new_name:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الأستاذ.")
                    return

                if not new_duties:
                    new_duties = "100"

                # تحديث قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE الاساتذة
                    SET اسم_الاستاذ = ?, نسبة_الواجبات = ?
                    WHERE id = ?
                """, (new_name, new_duties, teacher_id))

                conn.commit()
                conn.close()

                # تحديث الجدول
                self.load_teachers_registry()

                QMessageBox.information(self, "تم التحديث", "تم تحديث سجل الأستاذ بنجاح.")
                self.log_operation("تعديل سجل أستاذ", f"ID: {teacher_id}, الاسم: {new_name}")

        except Exception as e:
            self.handle_error("خطأ في تعديل سجل الأستاذ", e)

    def delete_teachers_registry(self):
        """حذف سجلات الأساتذة المحددة"""
        try:
            # الحصول على الصفوف المحددة
            selected_rows = set()
            for item in self.teachers_registry_table.selectedItems():
                selected_rows.add(item.row())

            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل أو أكثر للحذف.")
                return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف {len(selected_rows)} سجل؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # جمع معرفات السجلات المحددة
                teacher_ids = []
                for row in selected_rows:
                    teacher_id = int(self.teachers_registry_table.item(row, 0).text())
                    teacher_ids.append(teacher_id)

                # حذف السجلات من قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                for teacher_id in teacher_ids:
                    cursor.execute("DELETE FROM الاساتذة WHERE id = ?", (teacher_id,))

                conn.commit()
                conn.close()

                # تحديث الجدول
                self.load_teachers_registry()

                QMessageBox.information(
                    self,
                    "تم الحذف",
                    f"تم حذف {len(teacher_ids)} سجل بنجاح."
                )
                self.log_operation("حذف سجلات أساتذة", f"عدد السجلات: {len(teacher_ids)}")

        except Exception as e:
            self.handle_error("خطأ في حذف سجلات الأساتذة", e)

    def print_teachers_registry(self):
        """طباعة سجلات الأساتذة"""
        try:
            # استيراد ملف الطباعة
            import print111

            # إنشاء كائن الطباعة
            printer = print111.TeachersRegistryPrinter(self.db_path)

            # تنفيذ الطباعة
            printer.print_report()

            QMessageBox.information(self, "تم بنجاح", "تم إرسال تقرير سجلات الأساتذة للطباعة.")

        except ImportError:
            QMessageBox.critical(self, "خطأ", "ملف الطباعة print111.py غير موجود.")
        except Exception as e:
            self.handle_error("خطأ في طباعة سجلات الأساتذة", e)

    def export_teachers_registry(self):
        """تصدير سجلات الأساتذة"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ سجل الأساتذة",
                "سجل_الأساتذة.csv",
                "CSV files (*.csv)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8-sig', newline='') as file:
                    import csv
                    writer = csv.writer(file)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(self.teachers_registry_table.columnCount()):
                        headers.append(self.teachers_registry_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(self.teachers_registry_table.rowCount()):
                        if not self.teachers_registry_table.isRowHidden(row):
                            row_data = []
                            for col in range(self.teachers_registry_table.columnCount()):
                                item = self.teachers_registry_table.item(row, col)
                                row_data.append(item.text() if item else "")
                            writer.writerow(row_data)

                    QMessageBox.information(self, "تم التصدير", f"تم تصدير السجل بنجاح إلى:\n{filename}")

        except Exception as e:
            self.handle_error("خطأ في تصدير السجل", e)

    def center_on_screen(self):
        """توسيط النافذة في وسط الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget

        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def closeEvent(self, event):
        """إجراءات إغلاق النافذة"""
        try:
            self.log_operation("إغلاق النافذة")
            event.accept()
        except Exception as e:
            self.logger.error(f"خطأ في إغلاق النافذة: {str(e)}")
            event.accept()


def main():
    """الدالة الرئيسية لتشغيل النافذة"""
    app = QApplication(sys.argv)

    # تطبيق الخط العربي
    font = QFont("Calibri", 12)
    app.setFont(font)

    # إنشاء النافذة
    window = TeachersRegistryWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
