# -*- mode: python ; coding: utf-8 -*-
"""
ملف تحزيم محسّن لتجنب مشكلة اكتشاف الفيروسات الوهمية
تم تحسينه لتقليل الإيجابيات الخاطئة في برامج مكافحة الفيروسات

آخر تحديث: 2025-06-18
التحديثات الجديدة:
- إضافة ملف student_multi_registration.py (نافذة التسجيل المتعدد)
- إضافة ملف sub263_window.py (نافذة جديدة)
- إضافة ملف print_registration_fees_all_sections.py (تقرير جميع الأقسام)
- إضافة ملف run_student_registration.py (تشغيل التسجيل)
- إضافة ملف check_db_structure.py (فحص قاعدة البيانات)
- إضا<PERSON>ة قاعدة البيانات data.db
- إضا<PERSON>ة مكتبات جديدة: xlsxwriter, xlrd, dateutil, cryptography, psutil
- حذف الملفات غير الموجودة: budget_planning_window.py, multi_section_duties_window.py, sub232_window.py
"""

# تشفير قوي لتجنب اكتشاف الفيروسات الوهمية
import os
block_cipher = 'AES256' if os.environ.get('PYINSTALLER_ENCRYPT') else None

# إضافة مكتبات PDF بشكل صريح وشامل
import PyInstaller.utils.hooks as hookutils
import os
import sys

def collect_comprehensive_data():
    """جمع شامل لملفات البيانات للمكتبات"""
    collected_datas = []

    print("🚀 بدء عملية جمع البيانات الشاملة...")
    print("=" * 50)
    
    # قائمة شاملة لمكتبات PDF والنصوص العربية والمكتبات الجديدة
    pdf_packages = [
        'fpdf',           # مكتبة FPDF الأساسية
        'arabic_reshaper', # تشكيل النصوص العربية
        'bidi',           # اتجاه النصوص العربية
        'reportlab',      # مكتبة ReportLab للـ PDF المتقدم
        'PIL',            # معالجة الصور
        'matplotlib',     # الرسوم البيانية
        'openpyxl',       # ملفات Excel
        'xlsxwriter',     # كتابة ملفات Excel
        'xlrd',           # قراءة ملفات Excel القديمة
        'numpy',          # العمليات الرياضية
        'pandas',         # تحليل البيانات
        'PyQt5',          # واجهة المستخدم
        'sqlite3',        # قاعدة البيانات
        'dateutil',       # معالجة التواريخ المتقدمة
        'fontTools',      # أدوات الخطوط
        'cryptography',   # التشفير
        'psutil'          # معلومات النظام
    ]
    
    print("🔍 جمع ملفات البيانات للمكتبات...")
    
    for package in pdf_packages:
        try:
            # جمع ملفات البيانات
            package_datas = hookutils.collect_data_files(package)
            if package_datas:
                collected_datas.extend(package_datas)
                print(f"✅ تم جمع {len(package_datas)} ملف من {package}")
            
            # جمع الوحدات الفرعية
            try:
                submodules = hookutils.collect_submodules(package)
                if submodules:
                    print(f"✅ تم جمع {len(submodules)} وحدة فرعية من {package}")
            except:
                pass
                
        except Exception as e:
            print(f"⚠️ تعذر جمع ملفات {package}: {e}")
    
    # إضافة ملفات خاصة لـ ReportLab
    try:
        # ملفات الخطوط في ReportLab
        reportlab_fonts = hookutils.collect_data_files('reportlab.fonts')
        collected_datas.extend(reportlab_fonts)
        print("✅ تم جمع خطوط ReportLab")
    except:
        print("⚠️ تعذر جمع خطوط ReportLab")
    
    # إضافة ملفات خاصة لـ matplotlib
    try:
        matplotlib_data = hookutils.collect_data_files('matplotlib.mpl-data')
        collected_datas.extend(matplotlib_data)
        print("✅ تم جمع بيانات matplotlib")
    except:
        print("⚠️ تعذر جمع بيانات matplotlib")

    # إضافة ملفات خاصة لـ PyQt5
    try:
        pyqt5_data = hookutils.collect_data_files('PyQt5')
        collected_datas.extend(pyqt5_data)
        print("✅ تم جمع بيانات PyQt5")
    except:
        print("⚠️ تعذر جمع بيانات PyQt5")

    # إضافة ملفات خاصة للخطوط
    try:
        fonttools_data = hookutils.collect_data_files('fontTools')
        collected_datas.extend(fonttools_data)
        print("✅ تم جمع بيانات fontTools")
    except:
        print("⚠️ تعذر جمع بيانات fontTools")

    print("=" * 50)
    print(f"📊 إجمالي الملفات المجمعة: {len(collected_datas)}")
    print("✅ تم الانتهاء من جمع البيانات بنجاح!")
    print("=" * 50)
    return collected_datas

# جمع ملفات البيانات
collected_data = collect_comprehensive_data()

datas=[
    # الأيقونة
    ('01.ico', '.'),

    # ملفات النوافذ الرئيسية الموجودة حالياً
    ('main_window.py', '.'),
    ('sub01_window.py', '.'),
    ('sub2_window.py', '.'),
    ('sub8_window.py', '.'),
    ('sub252_window.py', '.'),
    ('sub262_window.py', '.'),
    ('sub263_window.py', '.'),  # ملف جديد
    ('sub26662_window.py', '.'),  # ملف سجلات الأساتذة المخصص
    ('sub3_window.py', '.'),
    ('sub4_window.py', '.'),
    ('sub100_window.py', '.'),
    ('expense_management_window.py', '.'),
    ('cash_flow_window.py', '.'),
    ('financial_system_launcher.py', '.'),
    ('monthly_duties_window.py', '.'),
    ('attendance_processing_window.py', '.'),
    ('student_multi_registration.py', '.'),  # ملف جديد مهم
    ('run_student_registration.py', '.'),    # ملف جديد
    ('check_db_structure.py', '.'),          # ملف جديد

    # ملفات الطباعة والتقارير الموجودة حالياً
    ('print101.py', '.'),
    ('print111.py', '.'),
    ('print144.py', '.'),
    ('print_registration_fees.py', '.'),
    ('print_registration_fees_monthly_style.py', '.'),
    ('print_registration_fees_simple.py', '.'),
    ('print_registration_fees_all_sections.py', '.'),  # ملف جديد
    ('print_section_monthly.py', '.'),
    ('print_section_yearly.py', '.'),
    ('attendance_sheet_report.py', '.'),
    ('daily_attendance_sheet_report.py', '.'),
    
    # قاعدة البيانات الرئيسية
    ('data.db', '.') if os.path.exists('data.db') else None,

    # ملفات إضافية مهمة
    ('version_info.txt', '.') if os.path.exists('version_info.txt') else None,

    # المجلدات الموجودة حالياً
    ('fonts/', 'fonts/'),
    ('logs/', 'logs/'),
    ('reports/', 'reports/'),

    # إضافة ملفات الخطوط العربية بشكل صريح
    ('fonts/Arial.ttf', 'fonts/') if os.path.exists('fonts/Arial.ttf') else None,
    ('fonts/arialbd.ttf', 'fonts/') if os.path.exists('fonts/arialbd.ttf') else None,
    ('fonts/calibri.ttf', 'fonts/') if os.path.exists('fonts/calibri.ttf') else None,
    ('fonts/calibrib.ttf', 'fonts/') if os.path.exists('fonts/calibrib.ttf') else None,

    # ملفات اختيارية قد تكون موجودة
    ('مجلد جديد/', 'مجلد جديد/') if os.path.exists('مجلد جديد/') else None,
    
] + collected_data

# تنظيف القائمة من العناصر الفارغة
datas = [item for item in datas if item is not None]

a = Analysis(
    ['main_window.py'],
    pathex=[r'c:\Users\<USER>\Desktop\taheri10'],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # النوافذ الرئيسية الموجودة حالياً
        'main_window',
        'sub01_window',
        'sub2_window',
        'sub8_window',
        'sub252_window',
        'sub262_window',
        'sub263_window',  # ملف جديد
        'sub26662_window',  # ملف سجلات الأساتذة المخصص
        'sub3_window',
        'sub4_window',
        'sub100_window',
        'expense_management_window',
        'cash_flow_window',
        'financial_system_launcher',
        'monthly_duties_window',
        'attendance_processing_window',
        'student_multi_registration',  # ملف جديد مهم
        'run_student_registration',    # ملف جديد
        'check_db_structure',          # ملف جديد

        # وحدات الطباعة والتقارير الموجودة حالياً
        'print101',
        'print111',
        'print144',
        'print_registration_fees',
        'print_registration_fees_monthly_style',
        'print_registration_fees_simple',
        'print_registration_fees_all_sections',  # ملف جديد
        'print_section_monthly',
        'print_section_yearly',
        'attendance_sheet_report',
        'daily_attendance_sheet_report',
        
        # مكتبات FPDF شاملة
        'fpdf',
        'fpdf.fpdf',
        'fpdf.fonts',
        'fpdf.image_parsing',
        'fpdf.line_break',
        'fpdf.outline',
        'fpdf.syntax',
        'fpdf.util',
        'fpdf.drawing',
        'fpdf.enums',
        'fpdf.errors',
        'fpdf.html',
        'fpdf.recorder',
        'fpdf.structure_tree',
        'fpdf.table',
        'fpdf.template',
        'fpdf.ttfonts',
        
        # مكتبات ReportLab شاملة
        'reportlab',
        'reportlab.pdfgen',
        'reportlab.pdfgen.canvas',
        'reportlab.pdfgen.textobject',
        'reportlab.pdfgen.pathobject',
        'reportlab.lib',
        'reportlab.lib.colors',
        'reportlab.lib.pagesizes',
        'reportlab.lib.styles',
        'reportlab.lib.units',
        'reportlab.lib.utils',
        'reportlab.lib.fonts',
        'reportlab.lib.enums',
        'reportlab.lib.validators',
        'reportlab.lib.formatters',
        'reportlab.platypus',
        'reportlab.platypus.paragraph',
        'reportlab.platypus.doctemplate',
        'reportlab.platypus.frames',
        'reportlab.platypus.flowables',
        'reportlab.platypus.tables',
        'reportlab.platypus.tableofcontents',
        'reportlab.platypus.xpreformatted',
        'reportlab.graphics',
        'reportlab.graphics.shapes',
        'reportlab.graphics.charts',
        'reportlab.graphics.charts.barcharts',
        'reportlab.graphics.charts.linecharts',
        'reportlab.graphics.charts.piecharts',
        'reportlab.graphics.renderPDF',
        'reportlab.graphics.renderPS',
        'reportlab.graphics.renderSVG',
        'reportlab.rl_config',
        'reportlab.pdfbase',
        'reportlab.pdfbase.pdfmetrics',
        'reportlab.pdfbase.ttfonts',
        'reportlab.pdfbase.cidfonts',
        'reportlab.pdfbase._fontdata',
        
        # مكتبات النصوص العربية شاملة
        'arabic_reshaper',
        'arabic_reshaper.arabic_reshaper',
        'arabic_reshaper.letters',
        'arabic_reshaper.ligatures',
        'bidi',
        'bidi.algorithm',
        'bidi.mirror',
        
        # مكتبات PIL شاملة
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'PIL.ImageFont',
        'PIL.ImageWin',
        'PIL.ImageTk',
        'PIL.ImageFilter',
        'PIL.ImageEnhance',
        'PIL.ImageOps',
        'PIL.ImageChops',
        'PIL.ImageColor',
        'PIL.ImageFile',
        'PIL.ImageGrab',
        'PIL.ImagePath',
        'PIL.ImageSequence',
        'PIL.ImageStat',
        'PIL.ImageTransform',
        'PIL._tkinter_finder',
        
        # مكتبات PyQt5
        'PyQt5',
        'PyQt5.QtWidgets',
        'PyQt5.QtGui',
        'PyQt5.QtCore',
        'PyQt5.QtPrintSupport',
        'PyQt5.sip',

        # مكتبات pywin32 للطباعة والنوافذ
        'win32print',
        'win32api',
        'win32con',
        'win32gui',
        'win32file',
        'win32pipe',
        'win32process',
        'win32security',
        'win32service',
        'win32serviceutil',
        'win32event',
        'win32evtlog',
        'win32clipboard',
        'win32timezone',
        'pywintypes',
        'pythoncom',
        'win32com',
        'win32com.client',
        
        # مكتبات النظام والبيانات
        'logging',
        'sqlite3',
        'openpyxl',
        'openpyxl.workbook',
        'openpyxl.worksheet',
        'openpyxl.styles',
        'matplotlib',
        'matplotlib.backends',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.figure',
        'matplotlib.pyplot',
        'numpy',
        'pandas',
        'datetime',
        'os',
        'sys',
        'json',
        'csv',
        'io',
        'base64',
        'tempfile',
        'pathlib',
        'collections',
        'itertools',
        'functools',
        'operator',
        'math',
        're',
        'string',
        'unicodedata',
        'codecs',
        'locale',
        'platform',
        'subprocess',
        'threading',
        'time',
        'traceback',
        'warnings',
        
        # مكتبات إضافية لـ PDF
        'zlib',
        'struct',
        'hashlib',
        'binascii',
        'gzip',
        'deflate',
        'array',
        'ctypes',
        'decimal',
        'fractions',
        'random',
        'uuid',
        'weakref',
        'copy',
        'pickle',
        'email',
        'email.mime',
        'email.mime.text',
        'email.mime.multipart',
        'urllib',
        'urllib.parse',
        'urllib.request',
        'http',
        'http.client',
        
        # مكتبات خاصة بالخطوط والترميز
        'encodings',
        'encodings.utf_8',
        'encodings.cp1256',
        'encodings.ascii',
        'encodings.latin_1',
        'fontTools',
        'fontTools.ttLib',
        'fontTools.subset',

        # مكتبات إضافية للتطبيق المحدث
        'xlsxwriter',           # لكتابة ملفات Excel
        'xlrd',                 # لقراءة ملفات Excel القديمة
        'openpyxl.reader',      # قارئ Excel
        'openpyxl.writer',      # كاتب Excel
        'openpyxl.utils',       # أدوات Excel
        'openpyxl.cell',        # خلايا Excel
        'openpyxl.formatting',  # تنسيق Excel

        # مكتبات التاريخ والوقت المحسنة
        'calendar',
        'dateutil',
        'dateutil.parser',
        'dateutil.relativedelta',

        # مكتبات الشبكة والاتصال
        'socket',
        'ssl',
        'certifi',

        # مكتبات التشفير والأمان
        'secrets',
        'hmac',
        'hashlib',
        'cryptography',

        # مكتبات معالجة النصوص المتقدمة
        'textwrap',
        'difflib',
        'stringprep',

        # مكتبات الذاكرة والأداء
        'gc',
        'resource',
        'psutil'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# وضع "one-folder" (مجلد واحد)
exe = EXE(
    pyz,
    a.scripts,
    [],  # فارغة لوضع المجلد الواحد
    exclude_binaries=True,  # مهم! يجب أن تكون True للمجلد الواحد
    name='المعين_في_الحراسة_العامة_PDF_نهائي',
    debug=False,  # إيقاف وضع التصحيح
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # إخفاء نافذة موجه الأوامر
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,  # للتوافق مع النظام الحالي
    codesign_identity=None,
    entitlements_file=None,
    icon='01.ico',
)

# جمع جميع الملفات في مجلد واحد
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='المعين_في_الحراسة_العامة_PDF_نهائي'
)
