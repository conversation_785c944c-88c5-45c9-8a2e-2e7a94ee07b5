#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import logging
import traceback
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem, 
    QFrame, QMessageBox, QComboBox, QFormLayout, QGroupBox, 
    QHeaderView, QInputDialog, QAbstractItemView, QSplitter,
    QTextEdit, QTabWidget, QSizePolicy
)
from PyQt5.QtGui import QFont, QIcon, QColor, QPalette
from PyQt5.QtCore import Qt, QSize

class SubjectsTeachersWindow(QMainWindow):
    """نافذة إدارة المواد والأساتذة والأقسام الجميلة والمنسقة"""
    
    def __init__(self, parent=None, db_path=None):
        super().__init__(parent)
        # تحديد مسار قاعدة البيانات
        if db_path:
            self.db_path = db_path
        elif parent and hasattr(parent, 'db_path'):
            self.db_path = parent.db_path
        else:
            # استخدام data.db في مجلد البرنامج
            script_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(script_dir, "data.db")
        self.setup_logging()
        self.logger.info("بدء تشغيل نافذة إدارة المواد والأساتذة والأقسام")
        
        try:
            self.setupUI()
            self.setup_database()
            self.load_groups()
            self.load_subjects()
            self.load_sections()
            self.load_teachers()
            self.center_on_screen()
            self.logger.info("تم تحميل النافذة بنجاح")
        except Exception as e:
            self.handle_critical_error("خطأ في تهيئة النافذة", e)
    
    def setup_logging(self):
        """إعداد نظام التسجيل لاستكشاف الأخطاء"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            log_dir = os.path.join(os.path.dirname(__file__), "logs")
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # إعداد ملف السجل
            log_file = os.path.join(log_dir, f"subjects_teachers_{datetime.now().strftime('%Y%m%d')}.log")
            
            # إعداد المسجل
            self.logger = logging.getLogger("SubjectsTeachersWindow")
            self.logger.setLevel(logging.DEBUG)
            
            # تنسيق الرسائل
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # معالج الملف
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            file_handler.setFormatter(formatter)
            
            # معالج وحدة التحكم
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(formatter)
            
            # إضافة المعالجات
            if not self.logger.handlers:
                self.logger.addHandler(file_handler)
                self.logger.addHandler(console_handler)
                
        except Exception as e:
            print(f"فشل في إعداد نظام التسجيل: {str(e)}")
            # إنشاء مسجل بسيط كبديل
            self.logger = logging.getLogger("SubjectsTeachersWindow")
            self.logger.setLevel(logging.INFO)
    
    def handle_critical_error(self, message, exception):
        """معالجة الأخطاء الحرجة"""
        error_details = f"{message}\n\nتفاصيل الخطأ:\n{str(exception)}\n\nالتتبع:\n{traceback.format_exc()}"
        self.logger.critical(f"{message}: {str(exception)}")
        self.logger.debug(f"تفاصيل الخطأ الكاملة:\n{traceback.format_exc()}")
        
        QMessageBox.critical(
            self, 
            "خطأ حرج", 
            f"{message}\n\n{str(exception)}\n\nيرجى التحقق من ملف السجل للحصول على مزيد من التفاصيل."
        )
    
    def handle_error(self, message, exception, show_user=True):
        """معالجة الأخطاء العادية"""
        error_msg = f"{message}: {str(exception)}"
        self.logger.error(error_msg)
        self.logger.debug(f"تفاصيل الخطأ:\n{traceback.format_exc()}")
        
        if show_user:
            QMessageBox.warning(self, "خطأ", f"{message}\n\n{str(exception)}")
    
    def log_operation(self, operation, details=""):
        """تسجيل العمليات"""
        log_msg = f"العملية: {operation}"
        if details:
            log_msg += f" - التفاصيل: {details}"
        self.logger.info(log_msg)

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة المواد الدراسية والأساتذة والأقسام")
        self.setFixedSize(1100, 650)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق نمط احترافي للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)        # العنوان الرئيسي
        title_label = QLabel("📚 إدارة المواد الدراسية والأساتذة والأقسام")
        title_label.setFont(QFont("Calibri", 14))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setMaximumHeight(70)
        title_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1e88e5,
                    stop: 0.5 #1976d2,
                    stop: 1 #1e88e5
                );
                color: white;
                padding: 15px;
                border-radius: 12px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Calibri", 14))
        self.tab_widget.setStyleSheet("""
            QTabWidget {
                background-color: transparent;
                border: none;
            }
            
            QTabWidget::pane {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
                padding: 10px;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1,
                    stop: 1 #d5dbdb
                );
                color: #2c3e50;
                padding: 12px 20px;
                margin: 2px;
                border-radius: 8px 8px 0px 0px;
                font-family: 'Calibri';
                
                font-weight: bold;
                min-width: 120px;
                border: 2px solid #bdc3c7;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
                border: 2px solid #1565c0;
                border-bottom: none;
                margin-bottom: -2px;
                padding-bottom: 14px;
            }
            
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                color: #1976d2;
                border: 2px solid #42a5f5;
                border-bottom: none;
            }
        """)
          # إضافة التبويبات
        self.setup_groups_tab()
        self.setup_subjects_tab()
        self.setup_sections_tab()
        self.setup_teachers_tab()
        self.setup_teachers_registry_tab()
        
        main_layout.addWidget(self.tab_widget)
        
    def setup_groups_tab(self):
        """إعداد تبويب المجموعات"""
        groups_tab = QWidget()
        layout = QVBoxLayout(groups_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # إطار إضافة مجموعة جديدة
        add_group_frame = QGroupBox("➕ إضافة مجموعة جديدة")
        add_group_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        add_group_frame.setStyleSheet(self.get_groupbox_style())
        add_group_frame.setMaximumHeight(120)
        
        add_layout = QHBoxLayout(add_group_frame)
        add_layout.setSpacing(10)
        
        self.new_group_input = QLineEdit()
        self.new_group_input.setPlaceholderText("أدخل اسم المجموعة الجديدة...")
        self.new_group_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.new_group_input.setMinimumHeight(35)
        self.new_group_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
            }
            QLineEdit:focus {
                border: 2px solid #7B1FA2;
                background-color: #f3e5f5;
            }
        """)
        self.new_group_input.returnPressed.connect(self.add_group)
        
        add_group_btn = self.create_styled_button("✨ إضافة المجموعة", "#9C27B0")
        add_group_btn.setMaximumWidth(150)
        add_group_btn.clicked.connect(self.add_group)
        
        add_layout.addWidget(QLabel("اسم المجموعة:"))
        add_layout.addWidget(self.new_group_input, 2)
        add_layout.addWidget(add_group_btn)
        
        layout.addWidget(add_group_frame)
        
        # جدول المجموعات
        groups_frame = QGroupBox("📋 المجموعات الموجودة")
        groups_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        groups_frame.setStyleSheet(self.get_groupbox_style())
        
        groups_layout = QVBoxLayout(groups_frame)
        
        # إنشاء جدول المجموعات
        self.groups_table = QTableWidget()
        self.groups_table.setColumnCount(3)
        self.groups_table.setHorizontalHeaderLabels(['ID', 'اسم المجموعة', 'تاريخ الإضافة'])
        
        # تنسيق جدول المجموعات
        self.groups_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.groups_table.setAlternatingRowColors(True)
        self.groups_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.groups_table.horizontalHeader().setStretchLastSection(True)
        self.groups_table.verticalHeader().setVisible(False)
        self.groups_table.setStyleSheet(self.get_table_style())
        
        # تعيين عرض الأعمدة
        header = self.groups_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المجموعة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        
        groups_layout.addWidget(self.groups_table)
        
        # أزرار العمليات على المجموعات
        groups_buttons_layout = QHBoxLayout()
        
        edit_group_btn = self.create_styled_button("✏️ تعديل المجموعة", "#4CAF50")
        edit_group_btn.clicked.connect(self.edit_group)
        
        delete_group_btn = self.create_styled_button("🗑️ حذف المجموعة", "#f44336")
        delete_group_btn.clicked.connect(self.delete_group)
        
        groups_buttons_layout.addWidget(edit_group_btn)
        groups_buttons_layout.addWidget(delete_group_btn)
        groups_buttons_layout.addStretch()
        
        groups_layout.addLayout(groups_buttons_layout)
        
        layout.addWidget(groups_frame)
        
        self.tab_widget.addTab(groups_tab, "المجموعات")

    def setup_subjects_tab(self):
        """إعداد تبويب المواد الدراسية"""
        subjects_tab = QWidget()
        layout = QVBoxLayout(subjects_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
          # إطار إضافة مادة جديدة
        add_subject_frame = QGroupBox("➕ إضافة مادة دراسية جديدة")
        add_subject_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        add_subject_frame.setStyleSheet(self.get_groupbox_style())
        add_subject_frame.setMaximumHeight(120)
        
        add_layout = QHBoxLayout(add_subject_frame)
        add_layout.setSpacing(10)
        self.new_subject_input = QLineEdit()
        self.new_subject_input.setPlaceholderText("أدخل اسم المادة الدراسية الجديدة...")
        self.new_subject_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.new_subject_input.setMinimumHeight(35)
        self.new_subject_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #42a5f5;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QLineEdit:focus {
                border: 2px solid #1976d2;
                background-color: #f8fbff;
            }
        """)
        self.new_subject_input.returnPressed.connect(self.add_subject)
        
        add_subject_btn = self.create_styled_button("✨ إضافة المادة", "#1976d2")
        add_subject_btn.setMaximumWidth(150)
        add_subject_btn.clicked.connect(self.add_subject)
        
        add_layout.addWidget(QLabel("اسم المادة:"))
        add_layout.addWidget(self.new_subject_input, 2)
        add_layout.addWidget(add_subject_btn)
        
        layout.addWidget(add_subject_frame)
          # جدول المواد
        subjects_frame = QGroupBox("📋 المواد الدراسية الموجودة")
        subjects_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        subjects_frame.setStyleSheet(self.get_groupbox_style())
        
        subjects_layout = QVBoxLayout(subjects_frame)
        
        # إنشاء جدول المواد
        self.subjects_table = QTableWidget()
        self.subjects_table.setColumnCount(3)
        self.subjects_table.setHorizontalHeaderLabels(['ID', 'اسم المادة', 'تاريخ الإضافة'])
          # تنسيق جدول المواد
        self.subjects_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.subjects_table.setAlternatingRowColors(True)
        self.subjects_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.subjects_table.horizontalHeader().setStretchLastSection(True)
        self.subjects_table.verticalHeader().setVisible(False)
        self.subjects_table.setStyleSheet(self.get_table_style())
        
        # تعيين عرض الأعمدة
        header = self.subjects_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المادة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        
        subjects_layout.addWidget(self.subjects_table)
        
        # أزرار العمليات على المواد
        subjects_buttons_layout = QHBoxLayout()
        
        edit_subject_btn = self.create_styled_button("✏️ تعديل المادة", "#4CAF50")
        edit_subject_btn.clicked.connect(self.edit_subject)
        
        delete_subject_btn = self.create_styled_button("🗑️ حذف المادة", "#f44336")
        delete_subject_btn.clicked.connect(self.delete_subject)
        
        subjects_buttons_layout.addWidget(edit_subject_btn)
        subjects_buttons_layout.addWidget(delete_subject_btn)
        subjects_buttons_layout.addStretch()
        
        subjects_layout.addLayout(subjects_buttons_layout)
        
        layout.addWidget(subjects_frame)
        
        self.tab_widget.addTab(subjects_tab, "المواد الدراسية")
        
    def setup_sections_tab(self):
        """إعداد تبويب الأقسام"""
        sections_tab = QWidget()
        layout = QVBoxLayout(sections_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
          # إطار إضافة قسم جديد
        add_section_frame = QGroupBox("📋 إضافة قسم جديد")
        add_section_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        add_section_frame.setStyleSheet(self.get_groupbox_style())
        add_section_frame.setMaximumHeight(120)
        
        add_layout = QHBoxLayout(add_section_frame)
        add_layout.setSpacing(10)
        
        self.new_section_input = QLineEdit()
        self.new_section_input.setPlaceholderText("أدخل اسم القسم الجديد...")
        self.new_section_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.new_section_input.setMinimumHeight(35)
        self.new_section_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #FF9800;
                border-radius: 8px;
                padding: 8px 12px;
               
            }
            QLineEdit:focus {
                border: 2px solid #F57C00;
                background-color: #fff8e1;
            }
        """)
        self.new_section_input.returnPressed.connect(self.add_section)
        
        add_section_btn = self.create_styled_button("✨ إضافة القسم", "#FF9800")
        add_section_btn.setMaximumWidth(150)
        add_section_btn.clicked.connect(self.add_section)
        
        add_layout.addWidget(QLabel("اسم القسم:"))
        add_layout.addWidget(self.new_section_input, 2)
        add_layout.addWidget(add_section_btn)
        
        layout.addWidget(add_section_frame)
          # جدول المواد
        sections_frame = QGroupBox("📋 الأقسام الموجودة")
        sections_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        sections_frame.setStyleSheet(self.get_groupbox_style())
        
        sections_layout = QVBoxLayout(sections_frame)
        
        # إنشاء جدول المواد
        self.sections_table = QTableWidget()
        self.sections_table.setColumnCount(3)
        self.sections_table.setHorizontalHeaderLabels(['ID', 'اسم القسم', 'تاريخ الإضافة'])
          # تنسيق جدول المواد
        self.sections_table.setFont(QFont("Calibri", 13))
        self.sections_table.setAlternatingRowColors(True)
        self.sections_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sections_table.horizontalHeader().setStretchLastSection(True)
        self.sections_table.verticalHeader().setVisible(False)
        self.sections_table.setStyleSheet(self.get_table_style())
        
        # تعيين عرض الأعمدة
        header = self.sections_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم القسم
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # تاريخ الإضافة
        
        sections_layout.addWidget(self.sections_table)
        
        # أزرار العمليات على الأقسام
        sections_buttons_layout = QHBoxLayout()

        edit_section_btn = self.create_styled_button("✏️ تعديل القسم", "#4CAF50")
        edit_section_btn.clicked.connect(self.edit_section)

        delete_section_btn = self.create_styled_button("🗑️ حذف القسم", "#f44336")
        delete_section_btn.clicked.connect(self.delete_section)

        # زر إضافة قسم آليا
        auto_add_section_btn = self.create_styled_button("🔄 إضافة قسم آليا", "#2196F3")
        auto_add_section_btn.clicked.connect(self.auto_add_section)

        sections_buttons_layout.addWidget(edit_section_btn)
        sections_buttons_layout.addWidget(delete_section_btn)
        sections_buttons_layout.addWidget(auto_add_section_btn)
        sections_buttons_layout.addStretch()
        
        sections_layout.addLayout(sections_buttons_layout)
        
        layout.addWidget(sections_frame)
        
        self.tab_widget.addTab(sections_tab, "الأقسام")
        
    def setup_teachers_tab(self):
        """إعداد تبويب الأساتذة"""
        teachers_tab = QWidget()
        layout = QVBoxLayout(teachers_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
          # إطار إضافة أستاذ جديد
        add_teacher_frame = QGroupBox("👨‍🏫 إضافة أستاذ جديد")
        add_teacher_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        add_teacher_frame.setStyleSheet(self.get_groupbox_style())
        add_teacher_frame.setMaximumHeight(350)  # زيادة الارتفاع لاستيعاب حقل المجموعة
        
        add_form_layout = QFormLayout(add_teacher_frame)
        add_form_layout.setSpacing(10)
        
        # حقل اسم الأستاذ
        self.new_teacher_name_input = QLineEdit()
        self.new_teacher_name_input.setPlaceholderText("أدخل اسم الأستاذ...")
        self.new_teacher_name_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.new_teacher_name_input.setMinimumHeight(35)
        self.new_teacher_name_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 8px 12px;
               
            }
            QLineEdit:focus {
                border: 2px solid #388e3c;
                background-color: #f8fff8;
            }
        """)
          # قائمة المواد
        self.teacher_subject_combo = QComboBox()
        self.teacher_subject_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.teacher_subject_combo.setMinimumHeight(35)
        self.teacher_subject_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QComboBox:focus {
                border: 2px solid #388e3c;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)        # قائمة الأقسام
        self.teacher_section_combo = QComboBox()
        self.teacher_section_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.teacher_section_combo.setMinimumHeight(35)
        self.teacher_section_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #FF9800;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QComboBox:focus {
                border: 2px solid #F57C00;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
        
        # قائمة المجموعات
        self.teacher_group_combo = QComboBox()
        self.teacher_group_combo.setFont(QFont("Calibri", 13, QFont.Bold))
        self.teacher_group_combo.setMinimumHeight(35)
        self.teacher_group_combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QComboBox:focus {
                border: 2px solid #7B1FA2;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)
          # حقل النسبة من الواجبات الشهرية
        self.teacher_duties_percent = QLineEdit()
        self.teacher_duties_percent.setPlaceholderText("أدخل النسبة المئوية...")
        self.teacher_duties_percent.setFont(QFont("Calibri", 13, QFont.Bold))
        self.teacher_duties_percent.setMinimumHeight(35)
        self.teacher_duties_percent.setInputMask("999%")  # تقييد الإدخال للأرقام فقط ونسبة مئوية
        self.teacher_duties_percent.setText("100%")  # قيمة افتراضية
        self.teacher_duties_percent.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #E91E63;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QLineEdit:focus {
                border: 2px solid #C2185B;
                background-color: #FCE4EC;
            }
        """)
        
        # زر الإضافة
        add_teacher_btn = self.create_styled_button("✨ إضافة الأستاذ", "#4CAF50")
        add_teacher_btn.clicked.connect(self.add_teacher)
        add_form_layout.addRow(self.create_form_label("اسم الأستاذ:"), self.new_teacher_name_input)
        add_form_layout.addRow(self.create_form_label("المادة:"), self.teacher_subject_combo)
        add_form_layout.addRow(self.create_form_label("القسم:"), self.teacher_section_combo)
        add_form_layout.addRow(self.create_form_label("المجموعة:"), self.teacher_group_combo)
        add_form_layout.addRow(self.create_form_label("النسبة من الواجبات:"), self.teacher_duties_percent)
        add_form_layout.addRow("", add_teacher_btn)
        
        layout.addWidget(add_teacher_frame)
        
        # إضافة عنصر لملء المساحة
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout.addWidget(spacer)
        self.tab_widget.addTab(teachers_tab, "الأساتذة والأقسام")
        
    def create_styled_button(self, text, color="#2196F3"):
        """إنشاء زر منسق"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMinimumHeight(35)
        button.setMinimumWidth(120)
        
        dark_color = self.darken_color(color, 30)
        light_color = self.lighten_color(color, 20)
        
        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 15px;
                
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
            }}        """)
        return button
        
    def create_form_label(self, text):
        """إنشاء تسمية للنموذج"""
        label = QLabel(text)
        label.setFont(QFont("Calibri", 13, QFont.Bold))
        label.setStyleSheet("color: #2c3e50; margin-right: 10px;")
        return label
        
    def get_groupbox_style(self):
        """نمط المجموعات"""
        return """
            QGroupBox {
                color: #1976d2;
                border: 2px solid #1976d2;
                border-radius: 10px;
                padding-top: 15px;
                margin-top: 10px;
                background-color: white;
                
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #1976d2;
                
            }
        """
        
    def get_table_style(self):
        """نمط الجداول"""
        return """
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #e3f2fd;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
                font-size: 16px;
            }

            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #1976d2;
            }

            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
                padding: 10px;
                border: none;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Calibri';
            }
        """

        
    def lighten_color(self, color, amount):
        """تفتيح اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون"""
        if color.startswith('#'):
            color = color[1:]
        
        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)
        
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.log_operation("بدء إعداد قاعدة البيانات", f"مسار قاعدة البيانات: {self.db_path}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحقق من إصدار SQLite
            cursor.execute("SELECT sqlite_version()")
            sqlite_version = cursor.fetchone()[0]
            self.logger.info(f"إصدار SQLite: {sqlite_version}")
            
            # إنشاء جدول المجموعات
            self.logger.debug("إنشاء جدول المجموعات")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS المجموعات (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_المجموعة TEXT UNIQUE NOT NULL,
                    تاريخ_الاضافة DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول المواد الدراسية
            self.logger.debug("إنشاء جدول المواد الدراسية")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS المواد_الدراسية (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_المادة TEXT UNIQUE NOT NULL,
                    تاريخ_الاضافة DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول الأقسام
            self.logger.debug("إنشاء جدول الأقسام")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_القسم TEXT UNIQUE NOT NULL,
                    تاريخ_الاضافة DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
              # إنشاء جدول الأساتذة المحدث
            self.logger.debug("إنشاء جدول الأساتذة")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الاساتذة (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الاستاذ TEXT NOT NULL,
                    مادة_id INTEGER NOT NULL,
                    قسم_id INTEGER,
                    نسبة_الواجبات INTEGER DEFAULT 100,
                    تاريخ_الاضافة DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (مادة_id) REFERENCES المواد_الدراسية (id),
                    FOREIGN KEY (قسم_id) REFERENCES الاقسام (id)
                )
            ''')
              # التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
            cursor.execute("PRAGMA table_info(الاساتذة)")
            columns_info = cursor.fetchall()
            has_section_column = any(col[1] == 'قسم_id' for col in columns_info)
            has_duties_column = any(col[1] == 'نسبة_الواجبات' for col in columns_info)
            has_group_column = any(col[1] == 'مجموعة_id' for col in columns_info)
            
            if not has_section_column:
                self.logger.info("إضافة عمود قسم_id إلى جدول الأساتذة")
                try:
                    cursor.execute("ALTER TABLE الاساتذة ADD COLUMN قسم_id INTEGER")
                    self.logger.info("تم إضافة عمود قسم_id بنجاح")
                except sqlite3.OperationalError as e:
                    self.logger.warning(f"العمود قسم_id موجود بالفعل أو خطأ في الإضافة: {e}")
              # التحقق من وجود عمود نسبة_الواجبات وإضافته إذا لم يكن موجود
            if not has_duties_column:
                self.logger.info("إضافة عمود نسبة_الواجبات إلى جدول الأساتذة")
                try:
                    cursor.execute("ALTER TABLE الاساتذة ADD COLUMN نسبة_الواجبات INTEGER DEFAULT 100")
                    self.logger.info("تم إضافة عمود نسبة_الواجبات بنجاح")
                except sqlite3.OperationalError as e:
                    self.logger.warning(f"العمود نسبة_الواجبات موجود بالفعل أو خطأ في الإضافة: {e}")
                    
            # التحقق من وجود عمود مجموعة_id وإضافته إذا لم يكن موجود
            if not has_group_column:
                self.logger.info("إضافة عمود مجموعة_id إلى جدول الأساتذة")
                try:
                    cursor.execute("ALTER TABLE الاساتذة ADD COLUMN مجموعة_id INTEGER")
                    cursor.execute("ALTER TABLE الاساتذة ADD CONSTRAINT FK_مجموعة FOREIGN KEY (مجموعة_id) REFERENCES المجموعات (id)")
                    self.logger.info("تم إضافة عمود مجموعة_id بنجاح")
                except sqlite3.OperationalError as e:
                    self.logger.warning(f"العمود مجموعة_id موجود بالفعل أو خطأ في الإضافة: {e}")
              # إنشاء جدول المواد والأقسام المحفوظة
            self.logger.debug("إنشاء جدول المواد والأقسام")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS جدول_المواد_والاقسام (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الاستاذ TEXT NOT NULL,
                    المادة TEXT NOT NULL,
                    القسم TEXT NOT NULL,
                    المجموعة TEXT,                    نسبة_الواجبات INTEGER DEFAULT 100,
                    تاريخ_الحفظ DATETIME DEFAULT CURRENT_TIMESTAMP,
                    ملاحظات TEXT,
                    UNIQUE(اسم_الاستاذ, القسم, المادة)
                )
            ''')
              # التحقق من وجود عمود المجموعة في جدول المواد والأقسام وإضافته إذا لم يكن موجود
            cursor.execute("PRAGMA table_info(جدول_المواد_والاقسام)")
            subjects_table_columns = cursor.fetchall()
            has_group_in_subjects = any(col[1] == 'المجموعة' for col in subjects_table_columns)
            
            if not has_group_in_subjects:
                self.logger.info("إضافة عمود المجموعة إلى جدول المواد والأقسام")
                try:
                    cursor.execute("ALTER TABLE جدول_المواد_والاقسام ADD COLUMN المجموعة TEXT")
                    self.logger.info("تم إضافة عمود المجموعة بنجاح")
                except sqlite3.OperationalError as e:
                    self.logger.warning(f"العمود المجموعة موجود بالفعل أو خطأ في الإضافة: {e}")
            
            # التحقق من وجود قيد الفريدة وإضافته إذا لم يكن موجود
            try:
                cursor.execute("PRAGMA index_list(جدول_المواد_والاقسام)")
                indexes = cursor.fetchall()
                has_unique_constraint = any('unique' in str(index).lower() for index in indexes)
                
                if not has_unique_constraint:
                    self.logger.info("إضافة قيد الفريدة لجدول المواد والأقسام")
                    cursor.execute('''
                        CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_teacher_subject_section 
                        ON جدول_المواد_والاقسام (اسم_الاستاذ, القسم, المادة)
                    ''')
                    self.logger.info("تم إضافة قيد الفريدة بنجاح")
            except sqlite3.OperationalError as e:
                self.logger.warning(f"قيد الفريدة موجود بالفعل أو خطأ في الإضافة: {e}")
                    
            # إضافة المجموعات الافتراضية
            default_groups = [
                "مجموعة مفتوحة",
                "مجموعة مغلقة", 
                "مجموعة المباريات",
                "مجموعة خاصة"
            ]
            
            self.logger.debug(f"إضافة {len(default_groups)} مجموعة افتراضية")
            for group in default_groups:
                cursor.execute('''
                    INSERT OR IGNORE INTO المجموعات (اسم_المجموعة) 
                    VALUES (?)
                ''', (group,))
            
            # إضافة المواد الافتراضية إذا لم تكن موجودة
            default_subjects = [
                "اللغة العربية", "اللغة الفرنسية", "اللغة الإنجليزية",
                "اللغة الإسبانية", "اللغة الألمانية", "اللغة الأمازيغية",
                "الرياضيات", "النشاط العلمي", "علوم الحياة والأرض",
                "العلوم الفيزيائية", "الإعلاميات", "التربية الإسلامية",
                "الاجتماعيات", "الجغرافيا", "الفلسفة", "المحاسبة",
                "الاقتصاد العام", "التنظيم الإداري", "القانون"
            ]
            
            self.logger.debug(f"إضافة {len(default_subjects)} مادة افتراضية")
            for subject in default_subjects:
                cursor.execute('''
                    INSERT OR IGNORE INTO المواد_الدراسية (اسم_المادة) 
                    VALUES (?)
                ''', (subject,))
              # إضافة الأقسام الافتراضية (50 قسم)
            default_sections = [f"قسم / {str(i).zfill(2)}" for i in range(1, 51)]
            
            self.logger.debug(f"إضافة {len(default_sections)} قسم افتراضي")
            for section in default_sections:
                cursor.execute('''
                    INSERT OR IGNORE INTO الاقسام (اسم_القسم) 
                    VALUES (?)
                ''', (section,))
            
            conn.commit()
            
            # تحقق من عدد السجلات المدرجة
            cursor.execute("SELECT COUNT(*) FROM المجموعات")
            groups_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM المواد_الدراسية")
            subjects_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM الاقسام")
            sections_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM الاساتذة")
            teachers_count = cursor.fetchone()[0]
            
            self.logger.info(f"إحصائيات قاعدة البيانات - المجموعات: {groups_count}, المواد: {subjects_count}, الأقسام: {sections_count}, الأساتذة: {teachers_count}")
            
            conn.close()
            self.log_operation("اكتمل إعداد قاعدة البيانات بنجاح")
            
        except sqlite3.Error as e:
            self.handle_critical_error("خطأ في قاعدة البيانات أثناء الإعداد", e)
        except Exception as e:
            self.handle_critical_error("خطأ غير متوقع في إعداد قاعدة البيانات", e)

    def load_groups(self):
        """تحميل المجموعات من قاعدة البيانات"""
        try:
            self.log_operation("بدء تحميل المجموعات")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM المجموعات ORDER BY اسم_المجموعة")
            groups = cursor.fetchall()
            
            self.logger.debug(f"تم العثور على {len(groups)} مجموعة في قاعدة البيانات")
              # ملء جدول المجموعات
            self.groups_table.setRowCount(len(groups))
            for row, group in enumerate(groups):
                self.groups_table.setItem(row, 0, QTableWidgetItem(str(group[0])))
                self.groups_table.setItem(row, 1, QTableWidgetItem(group[1]))
                self.groups_table.setItem(row, 2, QTableWidgetItem(str(group[2])))
            
            # ملء قائمة المجموعات في تبويب الأساتذة
            self.teacher_group_combo.clear()
            for group in groups:
                self.teacher_group_combo.addItem(group[1], group[0])  # النص، المعرف
            
            conn.close()
            self.log_operation("اكتمل تحميل المجموعات بنجاح", f"تم تحميل {len(groups)} مجموعة")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تحميل المجموعات", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تحميل المجموعات", e)

    def load_subjects(self):
        """تحميل المواد من قاعدة البيانات"""
        try:
            self.log_operation("بدء تحميل المواد الدراسية")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM المواد_الدراسية ORDER BY اسم_المادة")
            subjects = cursor.fetchall()
            
            self.logger.debug(f"تم العثور على {len(subjects)} مادة في قاعدة البيانات")
            
            # ملء جدول المواد
            self.subjects_table.setRowCount(len(subjects))
            for row, subject in enumerate(subjects):
                self.subjects_table.setItem(row, 0, QTableWidgetItem(str(subject[0])))
                self.subjects_table.setItem(row, 1, QTableWidgetItem(subject[1]))
                self.subjects_table.setItem(row, 2, QTableWidgetItem(str(subject[2])))
            
            # ملء قائمة المواد في تبويب الأساتذة
            self.teacher_subject_combo.clear()
            for subject in subjects:
                self.teacher_subject_combo.addItem(subject[1], subject[0])
            
            conn.close()
            self.log_operation("اكتمل تحميل المواد بنجاح", f"تم تحميل {len(subjects)} مادة")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تحميل المواد", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تحميل المواد", e)
            
    def load_sections(self):
        """تحميل الأقسام من قاعدة البيانات"""
        try:
            self.log_operation("بدء تحميل الأقسام")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM الاقسام ORDER BY اسم_القسم")
            sections = cursor.fetchall()
            
            self.logger.debug(f"تم العثور على {len(sections)} قسم في قاعدة البيانات")
            
            # ملء جدول الأقسام
            self.sections_table.setRowCount(len(sections))
            for row, section in enumerate(sections):
                self.sections_table.setItem(row, 0, QTableWidgetItem(str(section[0])))
                self.sections_table.setItem(row, 1, QTableWidgetItem(section[1]))
                self.sections_table.setItem(row, 2, QTableWidgetItem(str(section[2])))
            
            # ملء قائمة الأقسام في تبويب الأساتذة
            self.teacher_section_combo.clear()
            for section in sections:
                self.teacher_section_combo.addItem(section[1], section[0])
            
            conn.close()
            self.log_operation("اكتمل تحميل الأقسام بنجاح", f"تم تحميل {len(sections)} قسم")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تحميل الأقسام", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تحميل الأقسام", e)
            
    def load_teachers(self):
        """تحميل الأساتذة من قاعدة البيانات"""
        try:
            self.log_operation("بدء تحميل الأساتذة")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من وجود جدول الأساتذة وأعمدته
            cursor.execute("PRAGMA table_info(الاساتذة)")
            columns_info = cursor.fetchall()
            self.logger.debug(f"أعمدة جدول الأساتذة: {[col[1] for col in columns_info]}")
            
            # التحقق من وجود العمود قسم_id
            has_section_column = any(col[1] == 'قسم_id' for col in columns_info)
            
            # نلاحظ أننا لن نملأ أي جدول لأننا قمنا بإزالته من واجهة المستخدم
            # فقط نحصل على عدد الأساتذة للإحصائيات
            if has_section_column:
                cursor.execute('''
                    SELECT COUNT(*) 
                    FROM الاساتذة a 
                    JOIN المواد_الدراسية m ON a.مادة_id = m.id 
                    JOIN الاقسام s ON a.قسم_id = s.id
                ''')
            else:
                cursor.execute('''
                    SELECT COUNT(*) 
                    FROM الاساتذة a 
                    JOIN المواد_الدراسية m ON a.مادة_id = m.id
                ''')
            
            teachers_count = cursor.fetchone()[0]
            
            self.logger.debug(f"تم العثور على {teachers_count} أستاذ في قاعدة البيانات")
            
            conn.close()
            self.log_operation("اكتمل تحميل الأساتذة بنجاح", f"تم تحميل {teachers_count} أستاذ")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تحميل الأساتذة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تحميل الأساتذة", e)
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        try:
            group_name = self.new_group_input.text().strip()
            
            if not group_name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المجموعة.")
                return
            
            self.log_operation("محاولة إضافة مجموعة جديدة", f"اسم المجموعة: {group_name}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود المجموعة مسبقاً
            cursor.execute("SELECT COUNT(*) FROM المجموعات WHERE اسم_المجموعة = ?", (group_name,))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذه المجموعة موجودة بالفعل.")
                conn.close()
                return
            
            # إدراج المجموعة الجديدة
            cursor.execute('''
                INSERT INTO المجموعات (اسم_المجموعة, تاريخ_الاضافة) 
                VALUES (?, datetime('now'))
            ''', (group_name,))
            
            conn.commit()
            conn.close()
            
            # مسح الحقل وتحديث الجدول
            self.new_group_input.clear()
            self.load_groups()
            
            self.log_operation("تم إضافة المجموعة بنجاح", f"المجموعة: {group_name}")
            QMessageBox.information(self, "نجح", f"تم إضافة المجموعة '{group_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء إضافة المجموعة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في إضافة المجموعة", e)
    
    def edit_group(self):
        """تعديل مجموعة موجودة"""
        try:
            current_row = self.groups_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مجموعة للتعديل.")
                return
            
            group_id = int(self.groups_table.item(current_row, 0).text())
            old_name = self.groups_table.item(current_row, 1).text()
            
            new_name, ok = QInputDialog.getText(
                self, 
                "تعديل المجموعة", 
                "اسم المجموعة الجديد:",
                QLineEdit.Normal,
                old_name
            )
            
            if not ok or not new_name.strip():
                return
            
            new_name = new_name.strip()
            if new_name == old_name:
                return
            
            self.log_operation("محاولة تعديل المجموعة", f"من '{old_name}' إلى '{new_name}'")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود الاسم الجديد مسبقاً
            cursor.execute("SELECT COUNT(*) FROM المجموعات WHERE اسم_المجموعة = ? AND id != ?", 
                          (new_name, group_id))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذا الاسم موجود بالفعل.")
                conn.close()
                return
            
            # تحديث المجموعة
            cursor.execute('''
                UPDATE المجموعات 
                SET اسم_المجموعة = ? 
                WHERE id = ?
            ''', (new_name, group_id))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_groups()
            
            self.log_operation("تم تعديل المجموعة بنجاح", f"من '{old_name}' إلى '{new_name}'")
            QMessageBox.information(self, "نجح", f"تم تعديل المجموعة من '{old_name}' إلى '{new_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تعديل المجموعة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تعديل المجموعة", e)
    
    def delete_group(self):
        """حذف مجموعة موجودة"""
        try:
            current_row = self.groups_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مجموعة للحذف.")
                return
            
            group_id = int(self.groups_table.item(current_row, 0).text())
            group_name = self.groups_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المجموعة '{group_name}'؟\n\nملاحظة: قد يؤثر هذا على البيانات المرتبطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            self.log_operation("محاولة حذف المجموعة", f"المجموعة: {group_name} (ID: {group_id})")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف المجموعة
            cursor.execute("DELETE FROM المجموعات WHERE id = ?", (group_id,))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_groups()
            
            self.log_operation("تم حذف المجموعة بنجاح", f"المجموعة: {group_name}")
            QMessageBox.information(self, "نجح", f"تم حذف المجموعة '{group_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء حذف المجموعة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في حذف المجموعة", e)
    
    def add_subject(self):
        """إضافة مادة دراسية جديدة"""
        try:
            subject_name = self.new_subject_input.text().strip()
            
            if not subject_name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المادة الدراسية.")
                return
            
            self.log_operation("محاولة إضافة مادة دراسية جديدة", f"اسم المادة: {subject_name}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود المادة مسبقاً
            cursor.execute("SELECT COUNT(*) FROM المواد_الدراسية WHERE اسم_المادة = ?", (subject_name,))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذه المادة موجودة بالفعل.")
                conn.close()
                return
            
            # إدراج المادة الجديدة
            cursor.execute('''
                INSERT INTO المواد_الدراسية (اسم_المادة, تاريخ_الاضافة) 
                VALUES (?, datetime('now'))
            ''', (subject_name,))
            
            conn.commit()
            conn.close()
            
            # مسح الحقل وتحديث الجدول
            self.new_subject_input.clear()
            self.load_subjects()
            
            self.log_operation("تم إضافة المادة بنجاح", f"المادة: {subject_name}")
            QMessageBox.information(self, "نجح", f"تم إضافة المادة '{subject_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء إضافة المادة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في إضافة المادة", e)
    
    def edit_subject(self):
        """تعديل مادة دراسية موجودة"""
        try:
            current_row = self.subjects_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مادة للتعديل.")
                return
            
            subject_id = int(self.subjects_table.item(current_row, 0).text())
            old_name = self.subjects_table.item(current_row, 1).text()
            
            new_name, ok = QInputDialog.getText(
                self, 
                "تعديل المادة", 
                "اسم المادة الجديد:",
                QLineEdit.Normal,
                old_name
            )
            
            if not ok or not new_name.strip():
                return
            
            new_name = new_name.strip()
            if new_name == old_name:
                return
            
            self.log_operation("محاولة تعديل المادة", f"من '{old_name}' إلى '{new_name}'")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود الاسم الجديد مسبقاً
            cursor.execute("SELECT COUNT(*) FROM المواد_الدراسية WHERE اسم_المادة = ? AND id != ?", 
                          (new_name, subject_id))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذا الاسم موجود بالفعل.")
                conn.close()
                return
            
            # تحديث المادة
            cursor.execute('''
                UPDATE المواد_الدراسية 
                SET اسم_المادة = ? 
                WHERE id = ?
            ''', (new_name, subject_id))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_subjects()
            
            self.log_operation("تم تعديل المادة بنجاح", f"من '{old_name}' إلى '{new_name}'")
            QMessageBox.information(self, "نجح", f"تم تعديل المادة من '{old_name}' إلى '{new_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تعديل المادة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تعديل المادة", e)
    
    def delete_subject(self):
        """حذف مادة دراسية موجودة"""
        try:
            current_row = self.subjects_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مادة للحذف.")
                return
            
            subject_id = int(self.subjects_table.item(current_row, 0).text())
            subject_name = self.subjects_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المادة '{subject_name}'؟\n\nملاحظة: قد يؤثر هذا على البيانات المرتبطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            self.log_operation("محاولة حذف المادة", f"المادة: {subject_name} (ID: {subject_id})")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف المادة
            cursor.execute("DELETE FROM المواد_الدراسية WHERE id = ?", (subject_id,))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_subjects()
            
            self.log_operation("تم حذف المادة بنجاح", f"المادة: {subject_name}")
            QMessageBox.information(self, "نجح", f"تم حذف المادة '{subject_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء حذف المادة", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في حذف المادة", e)
    
    def add_section(self):
        """إضافة قسم جديد"""
        try:
            section_name = self.new_section_input.text().strip()
            
            if not section_name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم القسم.")
                return
            
            self.log_operation("محاولة إضافة قسم جديد", f"اسم القسم: {section_name}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود القسم مسبقاً
            cursor.execute("SELECT COUNT(*) FROM الاقسام WHERE اسم_القسم = ?", (section_name,))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذا القسم موجود بالفعل.")
                conn.close()
                return
            
            # إدراج القسم الجديد
            cursor.execute('''
                INSERT INTO الاقسام (اسم_القسم, تاريخ_الاضافة) 
                VALUES (?, datetime('now'))
            ''', (section_name,))
            
            conn.commit()
            conn.close()
            
            # مسح الحقل وتحديث الجدول
            self.new_section_input.clear()
            self.load_sections()
            
            self.log_operation("تم إضافة القسم بنجاح", f"القسم: {section_name}")
            QMessageBox.information(self, "نجح", f"تم إضافة القسم '{section_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء إضافة القسم", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في إضافة القسم", e)
    
    def edit_section(self):
        """تعديل قسم موجود"""
        try:
            current_row = self.sections_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد قسم للتعديل.")
                return
            
            section_id = int(self.sections_table.item(current_row, 0).text())
            old_name = self.sections_table.item(current_row, 1).text()
            
            new_name, ok = QInputDialog.getText(
                self, 
                "تعديل القسم", 
                "اسم القسم الجديد:",
                QLineEdit.Normal,
                old_name
            )
            
            if not ok or not new_name.strip():
                return
            
            new_name = new_name.strip()
            if new_name == old_name:
                return
            
            self.log_operation("محاولة تعديل القسم", f"من '{old_name}' إلى '{new_name}'")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # التحقق من عدم وجود الاسم الجديد مسبقاً
            cursor.execute("SELECT COUNT(*) FROM الاقسام WHERE اسم_القسم = ? AND id != ?", 
                          (new_name, section_id))
            if cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "هذا الاسم موجود بالفعل.")
                conn.close()
                return
            
            # تحديث القسم
            cursor.execute('''
                UPDATE الاقسام 
                SET اسم_القسم = ? 
                WHERE id = ?
            ''', (new_name, section_id))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_sections()
            
            self.log_operation("تم تعديل القسم بنجاح", f"من '{old_name}' إلى '{new_name}'")
            QMessageBox.information(self, "نجح", f"تم تعديل القسم من '{old_name}' إلى '{new_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تعديل القسم", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تعديل القسم", e)
    
    def delete_section(self):
        """حذف قسم موجود"""
        try:
            current_row = self.sections_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد قسم للحذف.")
                return
            
            section_id = int(self.sections_table.item(current_row, 0).text())
            section_name = self.sections_table.item(current_row, 1).text()
            
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف القسم '{section_name}'؟\n\nملاحظة: قد يؤثر هذا على البيانات المرتبطة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            self.log_operation("محاولة حذف القسم", f"القسم: {section_name} (ID: {section_id})")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # حذف القسم
            cursor.execute("DELETE FROM الاقسام WHERE id = ?", (section_id,))
            
            conn.commit()
            conn.close()
            
            # تحديث الجدول
            self.load_sections()
            
            self.log_operation("تم حذف القسم بنجاح", f"القسم: {section_name}")
            QMessageBox.information(self, "نجح", f"تم حذف القسم '{section_name}' بنجاح.")
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء حذف القسم", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في حذف القسم", e)
    def add_teacher(self):
        """إضافة أستاذ جديد"""
        try:
            teacher_name = self.new_teacher_name_input.text().strip()
            subject_id = self.teacher_subject_combo.currentData()
            section_id = self.teacher_section_combo.currentData()
            group_id = self.teacher_group_combo.currentData()
            duties_text = self.teacher_duties_percent.text().replace('%', '').strip()
            
            if not teacher_name:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الأستاذ.")
                return
            
            if not subject_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مادة دراسية.")
                return
            
            if not section_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار قسم.")
                return
                
            if not group_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة.")
                return
            
            # التحقق من صحة النسبة المئوية
            try:
                duties_percent = int(duties_text) if duties_text else 100
                if duties_percent < 0 or duties_percent > 100:
                    QMessageBox.warning(self, "تحذير", "يجب أن تكون النسبة المئوية بين 0 و 100.")
                    return
            except ValueError:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال نسبة مئوية صحيحة.")
                return
            
            self.log_operation("محاولة إضافة أستاذ جديد", f"الأستاذ: {teacher_name}")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على أسماء المادة والقسم والمجموعة بدلاً من معرفاتها
            cursor.execute("SELECT اسم_المادة FROM المواد_الدراسية WHERE id = ?", (subject_id,))
            subject_name = cursor.fetchone()[0]
            
            cursor.execute("SELECT اسم_القسم FROM الاقسام WHERE id = ?", (section_id,))
            section_name = cursor.fetchone()[0]
            
            cursor.execute("SELECT اسم_المجموعة FROM المجموعات WHERE id = ?", (group_id,))
            group_name = cursor.fetchone()[0]
              # التحقق من عدم وجود القسم مرتبط بمادة أخرى
            cursor.execute('''
                SELECT المادة FROM جدول_المواد_والاقسام
                WHERE القسم = ?
            ''', (section_name,))

            existing_subject = cursor.fetchone()
            if existing_subject:
                if existing_subject[0] != subject_name:
                    QMessageBox.warning(
                        self,
                        "تحذير - قسم مرتبط بمادة أخرى",
                        f"القسم '{section_name}' مرتبط بالفعل بالمادة '{existing_subject[0]}'.\n\n"
                        f"لا يمكن ربط نفس القسم بمادة أخرى.\n"
                        f"يرجى اختيار قسم آخر أو إلغاء ربط القسم من المادة السابقة أولاً."
                    )
                    conn.close()
                    return
                else:
                    QMessageBox.warning(
                        self,
                        "تحذير - قسم مسجل بالفعل",
                        f"القسم '{section_name}' مسجل بالفعل للمادة '{subject_name}'.\n\n"
                        f"لا يمكن تكرار نفس التسجيل."
                    )
                    conn.close()
                    return
            
            # إدراج البيانات في جدول المواد والأقسام
            cursor.execute('''
                INSERT INTO جدول_المواد_والاقسام 
                (اسم_الاستاذ, المادة, القسم, المجموعة, نسبة_الواجبات, تاريخ_الحفظ) 
                VALUES (?, ?, ?, ?, ?, datetime('now'))
            ''', (teacher_name, subject_name, section_name, group_name, duties_percent))
            
            conn.commit()
            conn.close()
            
            # مسح الحقول وتحديث البيانات
            self.new_teacher_name_input.clear()
            self.teacher_subject_combo.setCurrentIndex(0)
            self.teacher_section_combo.setCurrentIndex(0)
            self.teacher_group_combo.setCurrentIndex(0)
            self.teacher_duties_percent.setText("100%")
              # تحديث سجلات الأساتذة لعرض البيانات الجديدة
            self.load_teachers_registry()

            # تحديث القوائم المنسدلة في النافذة الحالية
            self.refresh_current_window_data()

            # تحديث جميع النماذج الأخرى بعد إضافة الأستاذ
            self.update_all_related_windows()

            self.log_operation("تم إضافة الأستاذ بنجاح", f"الأستاذ: {teacher_name} - القسم: {section_name}")
            QMessageBox.information(
                self,
                "نجح",
                f"تم تسجيل القسم '{section_name}' بنجاح.\n"
                f"الأستاذ: {teacher_name}\n"
                f"المادة: {subject_name}\n"
                f"المجموعة: {group_name}\n\n"
                f"✅ تم تحديث جميع النماذج المرتبطة"
            )
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء إضافة الأستاذ", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في إضافة الأستاذ", e)

    def refresh_current_window_data(self):
        """تحديث البيانات في النافذة الحالية بعد إضافة أستاذ"""
        try:
            print("🔄 تحديث البيانات في النافذة الحالية...")

            # تحديث قوائم الأقسام والمواد والمجموعات
            if hasattr(self, 'teacher_section_combo'):
                current_section = self.teacher_section_combo.currentText()
                self.load_sections()
                # استعادة التحديد السابق إذا كان متاحاً
                index = self.teacher_section_combo.findText(current_section)
                if index >= 0:
                    self.teacher_section_combo.setCurrentIndex(index)
                else:
                    self.teacher_section_combo.setCurrentIndex(0)

            if hasattr(self, 'teacher_subject_combo'):
                current_subject = self.teacher_subject_combo.currentText()
                self.load_subjects()
                # استعادة التحديد السابق إذا كان متاحاً
                index = self.teacher_subject_combo.findText(current_subject)
                if index >= 0:
                    self.teacher_subject_combo.setCurrentIndex(index)
                else:
                    self.teacher_subject_combo.setCurrentIndex(0)

            if hasattr(self, 'teacher_group_combo'):
                current_group = self.teacher_group_combo.currentText()
                self.load_groups()
                # استعادة التحديد السابق إذا كان متاحاً
                index = self.teacher_group_combo.findText(current_group)
                if index >= 0:
                    self.teacher_group_combo.setCurrentIndex(index)
                else:
                    self.teacher_group_combo.setCurrentIndex(0)

            # تحديث فلاتر سجلات الأساتذة
            if hasattr(self, 'update_subject_filter'):
                self.update_subject_filter()

            print("✅ تم تحديث البيانات في النافذة الحالية")

        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات في النافذة الحالية: {str(e)}")

    def update_all_related_windows(self):
        """تحديث جميع النماذج المرتبطة بعد إضافة أستاذ جديد"""
        try:
            print("🔄 بدء تحديث النماذج المرتبطة بعد إضافة أستاذ...")

            # الحصول على النافذة الرئيسية
            main_window = self.get_main_window()
            if not main_window:
                print("⚠️ لم يتم العثور على النافذة الرئيسية")
                return

            # قائمة النوافذ المطلوب تحديثها
            windows_to_update = [
                ("lists_sections", "sub252_window"),  # نافذة اللوائح والأقسام
                ("program_init", "sub232_window"),     # نافذة تهيئة البرنامج
                ("attendance_processing", "attendance_processing_window")  # نافذة مسك الغياب
            ]

            updated_count = 0

            for window_key, window_name in windows_to_update:
                try:
                    if hasattr(main_window, 'windows') and window_key in main_window.windows:
                        window = main_window.windows[window_key]
                        if window:
                            # محاولة تحديث النافذة بطرق مختلفة
                            updated = False

                            # الطريقة الأولى: refresh_data
                            if hasattr(window, 'refresh_data') and callable(getattr(window, 'refresh_data')):
                                window.refresh_data()
                                updated = True
                                print(f"✅ تم تحديث {window_name} باستخدام refresh_data")

                            # الطريقة الثانية: load_data
                            elif hasattr(window, 'load_data') and callable(getattr(window, 'load_data')):
                                window.load_data()
                                updated = True
                                print(f"✅ تم تحديث {window_name} باستخدام load_data")

                            # الطريقة الثالثة: update_data
                            elif hasattr(window, 'update_data') and callable(getattr(window, 'update_data')):
                                window.update_data()
                                updated = True
                                print(f"✅ تم تحديث {window_name} باستخدام update_data")

                            # الطريقة الرابعة: تحديث خاص لنافذة الأقسام (sub252_window)
                            elif window_name == "sub252_window":
                                # تحديث خيارات التصفية وقوائم الأقسام
                                if hasattr(window, 'load_filter_options') and callable(getattr(window, 'load_filter_options')):
                                    window.load_filter_options()
                                    updated = True
                                    print(f"✅ تم تحديث {window_name} باستخدام load_filter_options")

                                # تحديث إضافي لقوائم الأقسام في نوافذ التسجيل
                                if hasattr(window, 'registration_window') and window.registration_window:
                                    reg_window = window.registration_window
                                    if hasattr(reg_window, 'refresh_sections_combo') and callable(getattr(reg_window, 'refresh_sections_combo')):
                                        # استخدام الدالة الجديدة لتحديث قائمة الأقسام
                                        reg_window.refresh_sections_combo()
                                        print(f"✅ تم تحديث قائمة الأقسام في نافذة التسجيل باستخدام refresh_sections_combo")
                                    elif hasattr(reg_window, 'section_combo'):
                                        # الطريقة القديمة كبديل
                                        current_section = reg_window.section_combo.currentText()
                                        if hasattr(window, 'load_sections_to_combo'):
                                            window.load_sections_to_combo(reg_window.section_combo)
                                            # استعادة التحديد السابق
                                            index = reg_window.section_combo.findText(current_section)
                                            if index >= 0:
                                                reg_window.section_combo.setCurrentIndex(index)
                                        print(f"✅ تم تحديث قائمة الأقسام في نافذة التسجيل بالطريقة القديمة")

                            # الطريقة الخامسة: تحديث خاص لنافذة التهيئة (sub232_window)
                            elif window_name == "sub232_window":
                                # تحديث قائمة الأقسام باستخدام الدالة الجديدة
                                if hasattr(window, 'refresh_sections_combo') and callable(getattr(window, 'refresh_sections_combo')):
                                    window.refresh_sections_combo()
                                    updated = True
                                    print(f"✅ تم تحديث {window_name} باستخدام refresh_sections_combo")
                                # الطريقة القديمة كبديل
                                elif hasattr(window, 'load_sections_from_database') and callable(getattr(window, 'load_sections_from_database')):
                                    window.load_sections_from_database()
                                    updated = True
                                    print(f"✅ تم تحديث {window_name} باستخدام load_sections_from_database")

                            # الطريقة السادسة: تحديث خاص لنافذة الحضور والغياب (attendance_processing_window)
                            elif window_name == "attendance_processing_window":
                                if hasattr(window, 'load_sections') and callable(getattr(window, 'load_sections')):
                                    window.load_sections()
                                    updated = True
                                    print(f"✅ تم تحديث {window_name} باستخدام load_sections")

                                # تحديث إضافي شامل للبيانات
                                if hasattr(window, 'refresh_all_data') and callable(getattr(window, 'refresh_all_data')):
                                    window.refresh_all_data()
                                    print(f"✅ تم تحديث جميع البيانات في {window_name}")

                            if updated:
                                updated_count += 1
                            else:
                                print(f"⚠️ لم يتم العثور على طريقة تحديث مناسبة لـ {window_name}")
                        else:
                            print(f"⚠️ النافذة {window_name} غير موجودة")
                    else:
                        print(f"⚠️ مفتاح النافذة {window_key} غير موجود في النوافذ الرئيسية")

                except Exception as e:
                    print(f"❌ خطأ في تحديث النافذة {window_name}: {str(e)}")

            print(f"✅ تم تحديث {updated_count} نافذة من أصل {len(windows_to_update)}")

            # تحديث إضافي: البحث عن جميع نوافذ التسجيل المفتوحة وتحديثها
            self.update_all_registration_windows()

        except Exception as e:
            print(f"❌ خطأ عام في تحديث النوافذ المرتبطة: {str(e)}")

    def update_all_registration_windows(self):
        """تحديث جميع نوافذ التسجيل المفتوحة (MonthlyDutiesWindow)"""
        try:
            print("🔄 البحث عن نوافذ التسجيل المفتوحة لتحديثها...")

            # الحصول على جميع النوافذ المفتوحة في التطبيق
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if not app:
                return

            updated_windows = 0

            # البحث في جميع النوافذ المفتوحة
            for widget in app.allWidgets():
                # التحقق من أن النافذة هي من نوع MonthlyDutiesWindow
                if widget.__class__.__name__ == 'MonthlyDutiesWindow':
                    try:
                        # تحديث قائمة الأقسام في النافذة
                        if hasattr(widget, 'refresh_sections_combo') and callable(getattr(widget, 'refresh_sections_combo')):
                            widget.refresh_sections_combo()
                            updated_windows += 1
                            print(f"✅ تم تحديث نافذة تسجيل: {widget.windowTitle()}")
                        elif hasattr(widget, 'load_sections_from_database') and callable(getattr(widget, 'load_sections_from_database')):
                            widget.load_sections_from_database()
                            updated_windows += 1
                            print(f"✅ تم تحديث نافذة تسجيل (طريقة قديمة): {widget.windowTitle()}")
                    except Exception as e:
                        print(f"❌ خطأ في تحديث نافذة التسجيل: {str(e)}")

            if updated_windows > 0:
                print(f"✅ تم تحديث {updated_windows} نافذة تسجيل مفتوحة")
            else:
                print("ℹ️ لا توجد نوافذ تسجيل مفتوحة للتحديث")

        except Exception as e:
            print(f"❌ خطأ في تحديث نوافذ التسجيل المفتوحة: {str(e)}")

    def get_main_window(self):
        """الحصول على النافذة الرئيسية"""
        try:
            # البحث عن النافذة الرئيسية في التسلسل الهرمي
            parent = self.parent()
            while parent:
                if hasattr(parent, 'windows') and isinstance(parent.windows, dict):
                    return parent
                parent = parent.parent()
            return None
        except Exception as e:
            print(f"خطأ في الحصول على النافذة الرئيسية: {str(e)}")
            return None
        
    def setup_teachers_registry_tab(self):
        """إعداد تبويب سجلات الأساتذة"""
        registry_tab = QWidget()
        layout = QVBoxLayout(registry_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
          # قسم البحث والتصفية
        search_frame = QGroupBox("🔍 البحث والتصفية")
        search_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        search_frame.setStyleSheet(self.get_groupbox_style())
        search_frame.setMaximumHeight(120)
        
        search_layout = QHBoxLayout(search_frame)
        search_layout.setSpacing(15)
          # حقل البحث
        search_label = QLabel("البحث:")
        search_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_search_input = QLineEdit()
        self.registry_search_input.setPlaceholderText("ابحث بالاسم أو المادة أو القسم...")
        self.registry_search_input.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_search_input.setMinimumHeight(35)
        self.registry_search_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
                
            }
            QLineEdit:focus {
                border: 2px solid #7B1FA2;
                background-color: #f3e5f5;
            }
        """)
        self.registry_search_input.textChanged.connect(self.filter_teachers_registry)
        
        # تصفية حسب المادة
        filter_subject_label = QLabel("تصفية حسب المادة:")
        filter_subject_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_subject_filter = QComboBox()
        self.registry_subject_filter.setFont(QFont("Calibri", 13, QFont.Bold))
        self.registry_subject_filter.setMinimumHeight(35)
        self.registry_subject_filter.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #9C27B0;
                border-radius: 8px;
                padding: 8px 12px;
               
            }
            QComboBox:focus {
                border: 2px solid #7B1FA2;
            }
        """)
        self.registry_subject_filter.currentTextChanged.connect(self.filter_teachers_registry)
        
        # زر إعادة تعيين التصفية
        reset_filter_btn = self.create_styled_button("🔄 إعادة التعيين", "#9C27B0")
        reset_filter_btn.setMaximumWidth(140)
        reset_filter_btn.clicked.connect(self.reset_registry_filters)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.registry_search_input, 2)
        search_layout.addWidget(filter_subject_label)
        search_layout.addWidget(self.registry_subject_filter, 1)
        search_layout.addWidget(reset_filter_btn)
        
        layout.addWidget(search_frame)
          # جدول السجلات
        registry_frame = QGroupBox("📋 سجل الأساتذة المفصل")
        registry_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        registry_frame.setStyleSheet(self.get_groupbox_style())
        
        registry_layout = QVBoxLayout(registry_frame)
          # إنشاء جدول السجلات
        self.teachers_registry_table = QTableWidget()
        self.teachers_registry_table.setColumnCount(8)  # تحديث عدد الأعمدة لتشمل المجموعة
        self.teachers_registry_table.setHorizontalHeaderLabels([
            'رقم التسلسل', 'اسم الأستاذ', 'المادة', 'القسم', 'المجموعة', 'نسبة الواجبات', 'تاريخ التسجيل', 'الحالة'
        ])
          # تنسيق جدول السجلات
        self.teachers_registry_table.setFont(QFont("Calibri", 13, QFont.Bold))
        self.teachers_registry_table.setAlternatingRowColors(True)
        self.teachers_registry_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.teachers_registry_table.horizontalHeader().setStretchLastSection(True)
        self.teachers_registry_table.verticalHeader().setVisible(False)
        self.teachers_registry_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                gridline-color: #f0f0f0;
                selection-background-color: #e8f5e8;
            }
            
            QTableWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QTableWidget::item:selected {
                background-color: #c8e6c9;
                color: #1b5e20;
            }
            
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9C27B0,
                    stop: 1 #7B1FA2
                );
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                
        """)
        
        # تعيين عرض الأعمدة
        header = self.teachers_registry_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم التسلسل
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الأستاذ
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # المادة
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # القسم
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # تاريخ التسجيل
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # الحالة
        registry_layout.addWidget(self.teachers_registry_table)
        
        # قسم الإحصائيات
        stats_frame = QGroupBox("📈 الإحصائيات")
        stats_frame.setFont(QFont("Calibri", 14, QFont.Bold))
        stats_frame.setStyleSheet(self.get_groupbox_style())
        stats_frame.setMaximumHeight(100)
        
        stats_layout = QHBoxLayout(stats_frame)
        
        # إنشاء عناصر الإحصائيات
        self.total_teachers_label = QLabel("إجمالي الأساتذة: 0")
        self.total_teachers_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.total_teachers_label.setStyleSheet("color: #1976d2; padding: 8px;")
        
        self.active_teachers_label = QLabel("الأساتذة النشطين: 0")
        self.active_teachers_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.active_teachers_label.setStyleSheet("color: #4CAF50; padding: 8px;")
        
        self.subjects_count_label = QLabel("عدد المواد: 0")
        self.subjects_count_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.subjects_count_label.setStyleSheet("color: #FF9800; padding: 8px;")
        
        self.sections_count_label = QLabel("عدد الأقسام: 0")
        self.sections_count_label.setFont(QFont("Calibri", 13, QFont.Bold))
        self.sections_count_label.setStyleSheet("color: #9C27B0; padding: 8px;")
        
        stats_layout.addWidget(self.total_teachers_label)
        stats_layout.addWidget(QLabel(" | "))
        stats_layout.addWidget(self.active_teachers_label)
        stats_layout.addWidget(QLabel(" | "))
        stats_layout.addWidget(self.subjects_count_label)
        stats_layout.addWidget(QLabel(" | "))
        stats_layout.addWidget(self.sections_count_label)
        stats_layout.addStretch()
        
        registry_layout.addWidget(stats_frame)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()

        refresh_btn = self.create_styled_button("🔄 تحديث البيانات", "#4CAF50")
        refresh_btn.clicked.connect(self.refresh_teachers_registry)

        edit_btn = self.create_styled_button("✏️ تعديل سجل الأستاذ", "#2196F3")
        edit_btn.clicked.connect(self.edit_teacher_registry)

        delete_btn = self.create_styled_button("🗑️ حذف سجلات الأساتذة", "#f44336")
        delete_btn.clicked.connect(self.delete_teachers_registry)

        save_data_btn = self.create_styled_button("💾 حفظ البيانات", "#FF5722")
        save_data_btn.clicked.connect(self.save_teachers_data)

        # زر طباعة سجلات الأساتذة
        print_btn = self.create_styled_button("🖨️ طباعة سجلات الأساتذة", "#795548")
        print_btn.clicked.connect(self.print_teachers_registry)

        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(edit_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addWidget(save_data_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addStretch()
        
        registry_layout.addLayout(buttons_layout)
        
        layout.addWidget(registry_frame)
        
        self.tab_widget.addTab(registry_tab, "سجلات الأساتذة")
        
        # تحميل البيانات الأولية
        self.load_teachers_registry()
    
    def load_teachers_registry(self):
        """تحميل سجلات الأساتذة"""
        try:
            self.log_operation("تحميل سجلات الأساتذة")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على البيانات من الجدول القديم (الاساتذة) مع JOIN للحصول على الأسماء
            cursor.execute('''
                SELECT 
                    a.id,
                    a.اسم_الاستاذ,
                    m.اسم_المادة,
                    q.اسم_القسم,
                    g.اسم_المجموعة,
                    a.نسبة_الواجبات,
                    a.تاريخ_الاضافة
                FROM الاساتذة a
                LEFT JOIN المواد_الدراسية m ON a.مادة_id = m.id
                LEFT JOIN الاقسام q ON a.قسم_id = q.id
                LEFT JOIN المجموعات g ON a.مجموعة_id = g.id
                ORDER BY a.تاريخ_الاضافة DESC
            ''')
            
            old_teachers_data = cursor.fetchall()
            
            # الحصول على البيانات من الجدول الجديد (جدول_المواد_والاقسام)
            cursor.execute('''
                SELECT 
                    id,
                    اسم_الاستاذ,
                    المادة,
                    القسم,
                    المجموعة,
                    نسبة_الواجبات,
                    تاريخ_الحفظ
                FROM جدول_المواد_والاقسام
                ORDER BY تاريخ_الحفظ DESC
            ''')
            
            new_teachers_data = cursor.fetchall()
            
            # دمج البيانات من الجدولين
            all_teachers_data = []
            
            # إضافة البيانات من الجدول القديم مع تمييزها
            for teacher in old_teachers_data:
                teacher_list = list(teacher)
                all_teachers_data.append(teacher_list)
            
            # إضافة البيانات من الجدول الجديد
            for teacher in new_teachers_data:
                teacher_list = list(teacher)
                all_teachers_data.append(teacher_list)
            
            # ترتيب البيانات حسب التاريخ (الأحدث أولاً)
            all_teachers_data.sort(key=lambda x: x[6] or "1900-01-01", reverse=True)
            
            # تحديث الجدول
            self.teachers_registry_table.setColumnCount(8)
            self.teachers_registry_table.setHorizontalHeaderLabels([
                'رقم التسلسل', 'اسم الأستاذ', 'المادة', 'القسم', 'المجموعة', 'نسبة الواجبات', 'تاريخ التسجيل', 'الحالة'
            ])
            
            self.teachers_registry_table.setRowCount(len(all_teachers_data))
            
            for row, teacher in enumerate(all_teachers_data):
                teacher_id, name, subject, section, group, duties_percent, date_added = teacher
                
                # رقم التسلسل
                self.teachers_registry_table.setItem(row, 0, QTableWidgetItem(str(teacher_id)))
                
                # اسم الأستاذ
                self.teachers_registry_table.setItem(row, 1, QTableWidgetItem(name or "غير محدد"))
                
                # المادة
                self.teachers_registry_table.setItem(row, 2, QTableWidgetItem(subject or "غير محدد"))
                
                # القسم
                self.teachers_registry_table.setItem(row, 3, QTableWidgetItem(section or "غير محدد"))
                
                # المجموعة
                group_item = QTableWidgetItem(group or "غير محدد")
                group_item.setBackground(QColor("#f3e5f5"))  # لون خفيف بنفسجي للمجموعة
                self.teachers_registry_table.setItem(row, 4, group_item)
                
                # نسبة الواجبات
                duties_percent_item = QTableWidgetItem(f"{duties_percent or 100}%")
                self.teachers_registry_table.setItem(row, 5, duties_percent_item)
                
                # تاريخ التسجيل
                if date_added:
                    try:
                        from datetime import datetime
                        date_obj = datetime.fromisoformat(date_added.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_date = date_added
                else:
                    formatted_date = "غير محدد"
                
                self.teachers_registry_table.setItem(row, 6, QTableWidgetItem(formatted_date))
                
                # الحالة
                status = "نشط" if name and subject else "غير مكتمل"
                status_item = QTableWidgetItem(status)
                if status == "نشط":
                    status_item.setBackground(QColor("#e8f5e8"))
                    status_item.setForeground(QColor("#2e7d32"))
                else:
                    status_item.setBackground(QColor("#ffebee"))
                    status_item.setForeground(QColor("#c62828"))
                
                self.teachers_registry_table.setItem(row, 7, status_item)
            
            # تعيين عرض الأعمدة
            header = self.teachers_registry_table.horizontalHeader()
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم التسلسل
            header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الأستاذ
            header.setSectionResizeMode(2, QHeaderView.Stretch)  # المادة
            header.setSectionResizeMode(3, QHeaderView.Stretch)  # القسم
            header.setSectionResizeMode(4, QHeaderView.Stretch)  # المجموعة
            header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # نسبة الواجبات
            header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # تاريخ التسجيل
            header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # الحالة
            
            # تحديث فلتر المواد
            self.update_subject_filter()
            
            # تحديث الإحصائيات
            self.update_registry_statistics()
            
            conn.close()
            
        except Exception as e:
            self.handle_error("خطأ في تحميل سجلات الأساتذة", e)
    
    def update_subject_filter(self):
        """تحديث قائمة تصفية المواد"""
        try:
            current_text = self.registry_subject_filter.currentText()
            self.registry_subject_filter.clear()
            self.registry_subject_filter.addItem("جميع المواد")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT DISTINCT اسم_المادة FROM المواد_الدراسية ORDER BY اسم_المادة")
            subjects = cursor.fetchall()
            
            for subject in subjects:
                self.registry_subject_filter.addItem(subject[0])
            
            # استعادة التحديد السابق
            index = self.registry_subject_filter.findText(current_text)
            if index >= 0:
                self.registry_subject_filter.setCurrentIndex(index)
            
            conn.close()
            
        except Exception as e:
            self.handle_error("خطأ في تحديث فلتر المواد", e)
    
    def update_registry_statistics(self):
        """تحديث الإحصائيات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إجمالي الأساتذة من جدول_المواد_والاقسام
            cursor.execute("SELECT COUNT(DISTINCT اسم_الاستاذ) FROM جدول_المواد_والاقسام WHERE اسم_الاستاذ IS NOT NULL AND اسم_الاستاذ != ''")
            total_teachers_new = cursor.fetchone()[0]

            # إجمالي الأساتذة من الجدول القديم
            cursor.execute("SELECT COUNT(*) FROM الاساتذة WHERE اسم_الاستاذ IS NOT NULL AND اسم_الاستاذ != ''")
            total_teachers_old = cursor.fetchone()[0]

            # المجموع الكلي
            total_teachers = total_teachers_new + total_teachers_old

            # الأساتذة النشطين من جدول_المواد_والاقسام
            cursor.execute("""
                SELECT COUNT(DISTINCT اسم_الاستاذ) FROM جدول_المواد_والاقسام
                WHERE اسم_الاستاذ IS NOT NULL AND اسم_الاستاذ != ''
                AND المادة IS NOT NULL AND المادة != ''
            """)
            active_teachers_new = cursor.fetchone()[0]

            # الأساتذة النشطين من الجدول القديم
            cursor.execute("""
                SELECT COUNT(*) FROM الاساتذة a
                LEFT JOIN المواد_الدراسية m ON a.مادة_id = m.id
                WHERE a.اسم_الاستاذ IS NOT NULL AND a.اسم_الاستاذ != ''
                AND a.مادة_id IS NOT NULL
            """)
            active_teachers_old = cursor.fetchone()[0]

            # المجموع الكلي للنشطين
            active_teachers = active_teachers_new + active_teachers_old

            # عدد المواد
            cursor.execute("SELECT COUNT(*) FROM المواد_الدراسية")
            subjects_count = cursor.fetchone()[0]

            # عدد الأقسام
            cursor.execute("SELECT COUNT(*) FROM الاقسام")
            sections_count = cursor.fetchone()[0]

            # تحديث التسميات
            self.total_teachers_label.setText(f"إجمالي الأساتذة: {total_teachers}")
            self.active_teachers_label.setText(f"الأساتذة النشطين: {active_teachers}")
            self.subjects_count_label.setText(f"عدد المواد: {subjects_count}")
            self.sections_count_label.setText(f"عدد الأقسام: {sections_count}")

            conn.close()

        except Exception as e:
            self.handle_error("خطأ في تحديث الإحصائيات", e)
    
    def filter_teachers_registry(self):
        """تصفية سجلات الأساتذة"""
        try:
            search_text = self.registry_search_input.text().lower()
            subject_filter = self.registry_subject_filter.currentText()
            
            for row in range(self.teachers_registry_table.rowCount()):
                show_row = True
                
                # تطبيق فلتر البحث النصي
                if search_text:
                    row_text = ""
                    for col in range(self.teachers_registry_table.columnCount()):
                        item = self.teachers_registry_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "
                    
                    if search_text not in row_text:
                        show_row = False
                
                # تطبيق فلتر المادة
                if subject_filter and subject_filter != "جميع المواد":
                    subject_item = self.teachers_registry_table.item(row, 2)
                    if not subject_item or subject_item.text() != subject_filter:
                        show_row = False
                
                self.teachers_registry_table.setRowHidden(row, not show_row)
                
        except Exception as e:
            self.handle_error("خطأ في تصفية السجلات", e)
    
    def reset_registry_filters(self):
        """إعادة تعيين فلاتر البحث"""
        self.registry_search_input.clear()
        self.registry_subject_filter.setCurrentIndex(0)
        
        # إظهار جميع الصفوف
        for row in range(self.teachers_registry_table.rowCount()):
            self.teachers_registry_table.setRowHidden(row, False)
    
    def refresh_teachers_registry(self):
        """تحديث سجلات الأساتذة"""
        self.load_teachers_registry()
        QMessageBox.information(self, "تم التحديث", "تم تحديث سجلات الأساتذة بنجاح.")

    def edit_teacher_registry(self):
        """تعديل سجل أستاذ محدد"""
        try:
            # التحقق من تحديد صف
            current_row = self.teachers_registry_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل أستاذ للتعديل.")
                return

            # استخراج بيانات السجل المحدد
            teacher_id = int(self.teachers_registry_table.item(current_row, 0).text())
            current_name = self.teachers_registry_table.item(current_row, 1).text()
            current_subject = self.teachers_registry_table.item(current_row, 2).text()
            current_section = self.teachers_registry_table.item(current_row, 3).text()
            current_group = self.teachers_registry_table.item(current_row, 4).text()
            current_duties = self.teachers_registry_table.item(current_row, 5).text().replace('%', '')

            # إنشاء نافذة تعديل
            from PyQt5.QtWidgets import QDialog, QFormLayout, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل سجل الأستاذ")
            dialog.setFixedSize(400, 300)
            dialog.setLayoutDirection(Qt.RightToLeft)

            layout = QFormLayout(dialog)

            # حقل اسم الأستاذ
            name_input = QLineEdit(current_name)
            name_input.setFont(QFont("Calibri", 12))
            name_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #2196F3;
                    border-radius: 5px;
                }
            """)

            # حقل النسبة
            duties_input = QLineEdit(current_duties)
            duties_input.setFont(QFont("Calibri", 12))
            duties_input.setInputMask("999")  # أرقام فقط
            duties_input.setStyleSheet("""
                QLineEdit {
                    padding: 8px;
                    border: 2px solid #2196F3;
                    border-radius: 5px;
                }
            """)

            # حقل المجموعة (قابل للتعديل)
            group_input = QComboBox()
            group_input.setFont(QFont("Calibri", 12))
            group_input.setStyleSheet("""
                QComboBox {
                    padding: 8px;
                    border: 2px solid #2196F3;
                    border-radius: 5px;
                }
            """)

            # تحميل المجموعات المتاحة
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT اسم_المجموعة FROM المجموعات ORDER BY اسم_المجموعة")
            groups = cursor.fetchall()
            conn.close()

            group_input.addItem("اختر المجموعة")
            for group in groups:
                group_input.addItem(group[0])

            # تحديد المجموعة الحالية
            current_group_index = group_input.findText(current_group)
            if current_group_index >= 0:
                group_input.setCurrentIndex(current_group_index)

            # معلومات غير قابلة للتعديل
            subject_label = QLabel(current_subject)
            subject_label.setFont(QFont("Calibri", 12))
            subject_label.setStyleSheet("color: #666; padding: 8px; background: #f5f5f5; border-radius: 5px;")

            section_label = QLabel(current_section)
            section_label.setFont(QFont("Calibri", 12))
            section_label.setStyleSheet("color: #666; padding: 8px; background: #f5f5f5; border-radius: 5px;")

            # إضافة الحقول للنموذج
            layout.addRow("اسم الأستاذ:", name_input)
            layout.addRow("المادة:", subject_label)
            layout.addRow("القسم:", section_label)
            layout.addRow("المجموعة:", group_input)
            layout.addRow("النسبة (%):", duties_input)

            # أزرار الحوار
            button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
            button_box.button(QDialogButtonBox.Ok).setText("حفظ")
            button_box.button(QDialogButtonBox.Cancel).setText("إلغاء")
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)

            layout.addRow(button_box)

            # عرض النافذة
            if dialog.exec_() == QDialog.Accepted:
                new_name = name_input.text().strip()
                new_duties = duties_input.text().strip()
                new_group = group_input.currentText().strip()

                # التحقق من صحة البيانات
                if not new_name:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الأستاذ.")
                    return

                if new_group == "اختر المجموعة":
                    QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة.")
                    return

                try:
                    duties_percent = int(new_duties) if new_duties else 100
                    if duties_percent < 0 or duties_percent > 100:
                        QMessageBox.warning(self, "تحذير", "يجب أن تكون النسبة بين 0 و 100.")
                        return
                except ValueError:
                    QMessageBox.warning(self, "تحذير", "يرجى إدخال نسبة صحيحة.")
                    return

                # تحديث قاعدة البيانات
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # الحصول على معرف المجموعة من اسم المجموعة
                group_id = None
                if new_group and new_group != "اختر المجموعة":
                    cursor.execute("SELECT id FROM المجموعات WHERE اسم_المجموعة = ?", (new_group,))
                    group_result = cursor.fetchone()
                    if group_result:
                        group_id = group_result[0]

                # محاولة التحديث في جدول الأساتذة القديم أولاً
                cursor.execute("""
                    UPDATE الاساتذة
                    SET اسم_الاستاذ = ?, نسبة_الواجبات = ?, مجموعة_id = ?
                    WHERE id = ?
                """, (new_name, duties_percent, group_id, teacher_id))

                updated_old = cursor.rowcount > 0

                # إذا لم يتم العثور على السجل في الجدول القديم، حدث في الجدول الجديد
                if not updated_old:
                    cursor.execute("""
                        UPDATE جدول_المواد_والاقسام
                        SET اسم_الاستاذ = ?, نسبة_الواجبات = ?, المجموعة = ?
                        WHERE id = ?
                    """, (new_name, duties_percent, new_group, teacher_id))

                    updated_new = cursor.rowcount > 0

                    if not updated_new:
                        # محاولة التحديث بناءً على البيانات المطابقة
                        cursor.execute("""
                            UPDATE جدول_المواد_والاقسام
                            SET اسم_الاستاذ = ?, نسبة_الواجبات = ?, المجموعة = ?
                            WHERE اسم_الاستاذ = ? AND المادة = ? AND القسم = ?
                        """, (new_name, duties_percent, new_group, current_name, current_subject, current_section))

                conn.commit()

                if cursor.rowcount > 0:
                    conn.close()

                    # تحديث الجدول
                    self.load_teachers_registry()

                    # إظهار رسالة نجاح
                    self.log_operation("تم تعديل سجل الأستاذ بنجاح", f"تم تعديل سجل الأستاذ: {new_name}")
                    QMessageBox.information(
                        self,
                        "تم التعديل بنجاح",
                        f"تم تعديل سجل الأستاذ بنجاح.\n"
                        f"الاسم الجديد: {new_name}\n"
                        f"المجموعة الجديدة: {new_group}\n"
                        f"النسبة الجديدة: {duties_percent}%"
                    )
                else:
                    conn.close()
                    QMessageBox.warning(
                        self,
                        "فشل التعديل",
                        "لم يتم العثور على السجل المحدد للتعديل.\nيرجى تحديث البيانات والمحاولة مرة أخرى."
                    )

        except ValueError:
            QMessageBox.warning(self, "خطأ", "معرف السجل غير صحيح.")
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء تعديل سجل الأستاذ", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في تعديل سجل الأستاذ", e)

    def delete_teachers_registry(self):
        """حذف سجل أستاذ محدد"""
        try:
            # التحقق من تحديد صف
            current_row = self.teachers_registry_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد سجل أستاذ للحذف.")
                return

            # استخراج بيانات السجل المحدد
            teacher_id = int(self.teachers_registry_table.item(current_row, 0).text())
            teacher_name = self.teachers_registry_table.item(current_row, 1).text()
            subject_name = self.teachers_registry_table.item(current_row, 2).text()
            section_name = self.teachers_registry_table.item(current_row, 3).text()

            # تأكيد الحذف من المستخدم
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف سجل الأستاذ '{teacher_name}'؟\n"
                f"المادة: {subject_name}\n"
                f"القسم: {section_name}\n\n"
                f"لا يمكن التراجع عن هذه العملية!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # إجراء عملية الحذف
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # محاولة الحذف من جدول الأساتذة القديم أولاً
                cursor.execute("DELETE FROM الاساتذة WHERE id = ?", (teacher_id,))
                deleted_from_old = cursor.rowcount > 0

                # إذا لم يتم العثور على السجل في الجدول القديم، حاول الحذف من الجدول الجديد
                if not deleted_from_old:
                    cursor.execute("DELETE FROM جدول_المواد_والاقسام WHERE id = ?", (teacher_id,))
                    deleted_from_new = cursor.rowcount > 0

                    if not deleted_from_new:
                        # محاولة الحذف بناءً على البيانات المطابقة
                        cursor.execute("""
                            DELETE FROM جدول_المواد_والاقسام
                            WHERE اسم_الاستاذ = ? AND المادة = ? AND القسم = ?
                        """, (teacher_name, subject_name, section_name))

                conn.commit()

                # التحقق من نجاح الحذف
                if cursor.rowcount > 0:
                    conn.close()

                    # تحديث الجدول
                    self.load_teachers_registry()

                    # إظهار رسالة نجاح
                    self.log_operation("تم حذف سجل الأستاذ بنجاح", f"تم حذف سجل الأستاذ: {teacher_name}")
                    QMessageBox.information(
                        self,
                        "تم الحذف بنجاح",
                        f"تم حذف سجل الأستاذ '{teacher_name}' بنجاح."
                    )
                else:
                    conn.close()
                    QMessageBox.warning(
                        self,
                        "فشل الحذف",
                        f"لم يتم العثور على السجل المحدد للحذف.\nيرجى تحديث البيانات والمحاولة مرة أخرى."
                    )

        except ValueError:
            QMessageBox.warning(self, "خطأ", "معرف السجل غير صحيح.")
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء حذف سجل الأستاذ", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في حذف سجل الأستاذ", e)
    
    def export_teachers_registry(self):
        """تصدير سجلات الأساتذة"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ سجل الأساتذة", 
                "سجل_الأساتذة.csv",
                "CSV files (*.csv)"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8-sig', newline='') as file:
                    import csv
                    writer = csv.writer(file)
                    
                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(self.teachers_registry_table.columnCount()):
                        headers.append(self.teachers_registry_table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)
                    
                    # كتابة البيانات
                    for row in range(self.teachers_registry_table.rowCount()):
                        if not self.teachers_registry_table.isRowHidden(row):
                            row_data = []
                            for col in range(self.teachers_registry_table.columnCount()):
                                item = self.teachers_registry_table.item(row, col)
                                row_data.append(item.text() if item else "")
                            writer.writerow(row_data)
                
                QMessageBox.information(self, "تم التصدير", f"تم تصدير السجل بنجاح إلى:\n{filename}")
                
        except Exception as e:
            self.handle_error("خطأ في تصدير السجل", e)
    
    def print_teachers_registry(self):
        """طباعة سجل الأساتذة"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect
            
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)
                
                # إعداد الخط
                font = QFont("Arial", 10)
                painter.setFont(font)
                
                # طباعة العنوان
                title_font = QFont("Arial", 16, QFont.Bold)
                painter.setFont(title_font)
                painter.drawText(100, 100, "سجل الأساتذة")
                
                # طباعة البيانات
                y_pos = 200
                painter.setFont(font)
                
                for row in range(self.teachers_registry_table.rowCount()):
                    if not self.teachers_registry_table.isRowHidden(row):
                        line = ""
                        for col in range(self.teachers_registry_table.columnCount()):
                            item = self.teachers_registry_table.item(row, col)
                            line += f"{item.text() if item else ''}\t"
                        
                        painter.drawText(100, y_pos, line)
                        y_pos += 30
                        
                        if y_pos > 3000:  # صفحة جديدة
                            printer.newPage()
                            y_pos = 100
                
                painter.end()
                QMessageBox.information(self, "تمت الطباعة", "تم إرسال السجل للطباعة بنجاح.")
        except Exception as e:
            self.handle_error("خطأ في طباعة السجل", e)
    
    def save_teachers_data(self):
        """حفظ بيانات الأساتذة في جدول منفصل"""
        try:
            # التحقق من وجود بيانات للحفظ
            if self.teachers_registry_table.rowCount() == 0:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات أساتذة للحفظ.")
                return
            
            # تأكيد من المستخدم قبل الحفظ
            reply = QMessageBox.question(
                self, 
                "تأكيد الحفظ", 
                "هل تريد حفظ جميع بيانات الأساتذة في جدول منفصل؟\n"
                "سيتم حفظ البيانات الحالية مع الطابع الزمني.",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.Yes
            )
            
            if reply != QMessageBox.Yes:
                return
            
            self.log_operation("بدء حفظ بيانات الأساتذة في جدول منفصل")
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على جميع بيانات الأساتذة
            cursor.execute('''
                SELECT 
                    a.اسم_الاستاذ,
                    m.اسم_المادة,
                    q.اسم_القسم,
                    a.نسبة_الواجبات
                FROM الاساتذة a
                LEFT JOIN المواد_الدراسية m ON a.مادة_id = m.id
                LEFT JOIN الاقسام q ON a.قسم_id = q.id
                WHERE a.اسم_الاستاذ IS NOT NULL AND a.اسم_الاستاذ != ''
            ''')
            
            teachers_data = cursor.fetchall()
            
            if not teachers_data:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات صالحة للحفظ.")
                conn.close()
                return
            
            # حفظ البيانات في الجدول الجديد
            saved_count = 0
            for teacher in teachers_data:
                name, subject, section, duties_percent = teacher
                
                # التحقق من صحة البيانات
                if name and subject:
                    cursor.execute('''
                        INSERT INTO جدول_المواد_والاقسام 
                        (اسم_الاستاذ, المادة, القسم, نسبة_الواجبات, ملاحظات) 
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        name, 
                        subject or "غير محدد", 
                        section or "غير محدد", 
                        duties_percent or 100,
                        f"محفوظ من سجل الأساتذة في {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    ))
                    saved_count += 1
            
            conn.commit()
            conn.close()
            
            self.log_operation(
                "تم حفظ بيانات الأساتذة بنجاح", 
                f"تم حفظ {saved_count} سجل أستاذ في جدول المواد والأقسام"
            )
            
            QMessageBox.information(
                self, 
                "تم الحفظ بنجاح", 
                f"تم حفظ {saved_count} سجل أستاذ في جدول المواد والأقسام بنجاح.\n\n"
                f"يمكنك الآن الاطلاع على البيانات المحفوظة في قاعدة البيانات."
            )
            
        except sqlite3.Error as e:
            self.handle_error("خطأ في قاعدة البيانات أثناء حفظ البيانات", e)
        except Exception as e:
            self.handle_error("خطأ غير متوقع في حفظ البيانات", e)
                
    def center_on_screen(self):
        """توسيط النافذة في وسط الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        
        screen = QDesktopWidget().screenGeometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def closeEvent(self, event):
        """إجراءات إغلاق النافذة"""
        try:
            self.log_operation("إغلاق النافذة")
            event.accept()
        except Exception as e:
            self.logger.error(f"خطأ أثناء إغلاق النافذة: {str(e)}")
            event.accept()

    def auto_add_section(self):
        """إضافة قسم آليا بناءً على أعلى قيمة موجودة"""
        try:
            # الحصول على اسم القسم المحدد من المستخدم
            section_name, ok = QInputDialog.getText(
                self,
                "إضافة قسم آليا",
                "أدخل اسم القسم الأساسي (مثل: قسم):",
                text="قسم"
            )

            if not ok or not section_name.strip():
                return

            section_name = section_name.strip()

            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن أعلى رقم للقسم المحدد
            cursor.execute("""
                SELECT اسم_القسم FROM الاقسام
                WHERE اسم_القسم LIKE ?
                ORDER BY اسم_القسم DESC
            """, (f"{section_name}%",))

            results = cursor.fetchall()
            max_number = 0

            # البحث عن أعلى رقم
            for result in results:
                section_full_name = result[0]
                # محاولة استخراج الرقم من نهاية اسم القسم
                parts = section_full_name.split()
                if len(parts) >= 2:
                    try:
                        # البحث عن الرقم في الجزء الأخير
                        last_part = parts[-1]
                        if last_part.isdigit():
                            number = int(last_part)
                            max_number = max(max_number, number)
                        elif "/" in last_part:
                            # التعامل مع الأرقام المفصولة بـ /
                            number_parts = last_part.split("/")
                            for part in number_parts:
                                if part.isdigit():
                                    number = int(part)
                                    max_number = max(max_number, number)
                    except ValueError:
                        continue

            # إنشاء اسم القسم الجديد
            new_number = max_number + 1
            new_section_name = f"{section_name} / {new_number}"

            # إضافة القسم الجديد
            cursor.execute("""
                INSERT INTO الاقسام (اسم_القسم, تاريخ_الاضافة)
                VALUES (?, datetime('now'))
            """, (new_section_name,))

            conn.commit()
            conn.close()

            # تحديث الجدول
            self.load_sections()

            # رسالة نجاح
            QMessageBox.information(
                self,
                "تم بنجاح",
                f"تم إضافة القسم الجديد: {new_section_name}\n"
                f"أعلى رقم سابق كان: {max_number}"
            )

            self.log_operation("إضافة قسم آليا", f"تم إضافة: {new_section_name}")

        except Exception as e:
            self.handle_error("خطأ في إضافة القسم آليا", e)

    def print_teachers_registry(self):
        """طباعة سجلات الأساتذة"""
        try:
            # استيراد ملف الطباعة
            import print111

            # إنشاء كائن الطباعة
            printer = print111.TeachersRegistryPrinter(self.db_path)

            # تنفيذ الطباعة
            printer.print_report()

            QMessageBox.information(self, "تم بنجاح", "تم إرسال تقرير سجلات الأساتذة للطباعة.")

        except ImportError:
            QMessageBox.critical(self, "خطأ", "ملف الطباعة print111.py غير موجود.")
        except Exception as e:
            self.handle_error("خطأ في طباعة سجلات الأساتذة", e)

# تشغيل التطبيق للاختبار
if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = SubjectsTeachersWindow()
    window.show()
    
    sys.exit(app.exec_())
